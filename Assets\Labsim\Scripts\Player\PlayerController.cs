using System;
using UnityEngine;
using Unity.Cinemachine;
using PurrNet;
using System.Collections.Generic;
using UnityEngine.Rendering;

[RequireComponent(typeof(CharacterController))]
public class PlayerController : NetworkBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float moveSpeed = 5f;
    [SerializeField] private float sprintSpeed = 8f;
    [SerializeField] private float jumpForce = 1f;
    [SerializeField] private float gravity = -9.81f;
    [SerializeField] private float groundCheckDistance = 0.2f;

    [Header("Look Settings")]
    [SerializeField] private float lookSensitivity = 2f;
    [SerializeField] private float maxLookAngle = 80f;

    [Header("References")]
    [SerializeField] private CinemachineCamera playerCamera;

    [SerializeField] private PlayerInputHandler playerInputHandler;
    [SerializeField] private GameObject playerCrosshair;


    [SerializeField] private List<Renderer> renderers = new();

    private CharacterController characterController;
    private Vector3 velocity;
    private float verticalRotation = 0f;

    protected override void OnSpawned()
    {
        base.OnSpawned();
        enabled = isOwner;
        playerCamera.gameObject.SetActive(isOwner);
        DisableSelfRender();
        CursorLock();
    }

    private void Start()
    {
        characterController = GetComponent<CharacterController>();
        playerInputHandler = GetComponent<PlayerInputHandler>();

        if (playerCamera == null)
        {
            enabled = false;
            return;
        }
    }

    private void Update()
    {
        HandleMovement();
        HandleRotation();
    }

    private void HandleMovement()
    {
        bool isGrounded = IsGrounded();
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }

        float horizontal = playerInputHandler.playerInput.Player.Move.ReadValue<Vector2>().x;
        float vertical = playerInputHandler.playerInput.Player.Move.ReadValue<Vector2>().y;

        Vector3 moveDirection = transform.right * horizontal + transform.forward * vertical;
        moveDirection = Vector3.ClampMagnitude(moveDirection, 1f);

        float currentSpeed = playerInputHandler.playerInput.Player.Sprint.ReadValue<float>() > 0.5f ? sprintSpeed : moveSpeed;
        characterController.Move(moveDirection * currentSpeed * Time.deltaTime);

        if (playerInputHandler.playerInput.Player.Jump.triggered && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpForce * -2f * gravity);
        }

        velocity.y += gravity * Time.deltaTime;
        characterController.Move(velocity * Time.deltaTime);
    }

    private void HandleRotation()
    {

        float mouseX = playerInputHandler.playerInput.Player.Look.ReadValue<Vector2>().x * lookSensitivity;

        float mouseY = playerInputHandler.playerInput.Player.Look.ReadValue<Vector2>().y * lookSensitivity;

        verticalRotation += mouseY;
        verticalRotation = Mathf.Clamp(verticalRotation, -maxLookAngle, maxLookAngle);
        playerCamera.transform.localRotation = Quaternion.Euler(verticalRotation, 0f, 0f);

        transform.Rotate(Vector3.up * mouseX);
    }

    private bool IsGrounded()
    {
        return Physics.Raycast(transform.position + Vector3.up * 0.03f, Vector3.down, groundCheckDistance);
    }


    void DisableSelfRender()
    {
        if (isOwner)
        {
            foreach (var render in renderers)
            {
                render.shadowCastingMode = ShadowCastingMode.ShadowsOnly;
            }
        }
    }

    public void PlayerCrosshair(bool state)
    {
       playerCrosshair.SetActive(state);
    }
    

    public void CursorLock()
    {
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    public void CursorUnlock()
    {
        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }
}