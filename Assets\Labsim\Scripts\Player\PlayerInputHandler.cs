using UnityEngine;
using PurrNet;

public class PlayerInputHandler : NetworkBehaviour
{

    public PlayerInput playerInput;

    protected override void OnSpawned()
    {
        base.OnSpawned();
        enabled = isOwner;
        if (isOwner)
        {
            playerInput = new PlayerInput();
            playerInput.Player.Enable();
        }
    }

    protected override void OnDespawned()
    {
        base.OnDespawned();
        if (playerInput != null)
        {
            playerInput.Player.Disable();
            playerInput.Dispose();
            playerInput = null;
        }
    }

}
