using UnityEngine;
using System;
using UnityEngine.InputSystem;
using PurrNet;

public class PlayerInteractor : NetworkBehaviour, IInteractor
{
    [Header("Ray Settings")]
    [SerializeField] private Transform rayOrigin;
    [SerializeField] private float rayDistance = 5f;
    [SerializeField] private LayerMask interactableLayer;

    //References
    public PlayerInputHandler playerInputHandler;
    
    // Properties
    public GameObject GameObject => gameObject;
    public Transform Transform => transform;

    [SerializeField] private string m_InteractionKey = "E";
    public string InteractionKey => m_InteractionKey;


    public PlayerID playerID => networkManager.localPlayer;
    // State
    private IInteractable currentTarget;
    private IInteractable previousTarget;
    private bool previousCanInteract;
    
    // Events
    public static Action<IInteractable> OnTargetChanged;
    public static Action<IInteractable> OnInteractionPerformed;
    public static Action<IInteractable,string> OnInteractionHoverEnter;
    public static Action<IInteractable> OnInteractionHoverExit;


    void Start()
    {
        playerInputHandler = GetComponent<PlayerInputHandler>();
    }



    protected override void OnSpawned()
    {
        base.OnSpawned();
        enabled = isOwner;

        if (!isOwner) return;

        // Setup input handler
        if (playerInputHandler != null && playerInputHandler.playerInput != null)
        {
            playerInputHandler.playerInput.Player.Interact.performed += HandleInteract;
        }

        if (rayOrigin == null)
        {
            GameObject cameraObj = GameObject.FindGameObjectWithTag("PlayerMainCamera");
            if (cameraObj != null)
            {
                Camera mainCam = cameraObj.GetComponent<Camera>();
                if (mainCam != null)
                {
                    rayOrigin = mainCam.transform;
                }
                else
                {
                    Debug.LogWarning("PlayerMainCamera tag found but no Camera component. Using transform as ray origin.");
                }
            }
            else
            {
                Debug.LogWarning("No PlayerMainCamera tag found. Using transform as ray origin.");
            }
        }

        // Setup layer
        if (interactableLayer == 0)
        {
            interactableLayer = LayerMask.GetMask("Interactable");
            if (interactableLayer == 0)
            {
                Debug.LogError("Interactable layer not found! Please create an 'Interactable' layer.");
            }
        }
    }

    void Update()
    {
        if (!isOwner) return;
        PerformRaycast();
        CheckInteractionStateChange();
    }
    
    private void PerformRaycast()
    {
        Ray ray = new Ray(rayOrigin.position, rayOrigin.forward);
        IInteractable detected = null;
        
        if (Physics.Raycast(ray, out RaycastHit hit, rayDistance, interactableLayer))
        {
            detected = hit.collider.GetComponent<IInteractable>();
        }
        
        UpdateTarget(detected);  
    }
    
    private void UpdateTarget(IInteractable newTarget)
    {
        
        if (newTarget == previousTarget) return;
        
       
        if (previousTarget != null)
        {
            previousTarget.OnHoverExit(this);
            OnInteractionHoverExit?.Invoke(previousTarget);
        }
        
        
        if (newTarget != null)
        {
            newTarget.OnHoverEnter(this);
            OnTargetChanged?.Invoke(newTarget);
        
            OnInteractionHoverEnter?.Invoke(newTarget, InteractionKey);
        }
        
        currentTarget = newTarget;
        previousTarget = newTarget;
        previousCanInteract = newTarget?.CanInteract ?? false;
    }
    
    private void CheckInteractionStateChange()
    {
        if (currentTarget != null)
        {
            bool currentCanInteract = currentTarget.CanInteract;
            if (currentCanInteract != previousCanInteract)
            {
                previousCanInteract = currentCanInteract;
                
                
                OnInteractionHoverEnter?.Invoke(currentTarget, InteractionKey);
            }
        }
    }

    void HandleInteract(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            TryInteract();
        }
    }

    
    public void TryInteract()
    {
        if (currentTarget != null && currentTarget.CanInteract)
        {
            currentTarget.OnInteract(this);
            OnInteractionPerformed?.Invoke(currentTarget);
        }
    }

    protected override void OnDespawned()
    {
        base.OnDespawned();
        
        // Clean up input events
        if (playerInputHandler != null && playerInputHandler.playerInput != null)
        {
            playerInputHandler.playerInput.Player.Interact.performed -= HandleInteract;
        }
        
        // Clean up interaction targets
        if (currentTarget != null)
        {
            currentTarget.OnHoverExit(this);
            currentTarget = null;
        }
        
        previousTarget = null;
    }
    
}