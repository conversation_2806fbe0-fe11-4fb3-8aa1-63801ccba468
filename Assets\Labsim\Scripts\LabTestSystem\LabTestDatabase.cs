using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;
using Newtonsoft.Json;
using PurrNet;

namespace LabTestSystem
{
    public class LabTestDatabase : NetworkBehaviour
    {
        

        [Header("Test Definitions")]
        [SerializeField] private List<TestDefinitionSO> availableTestDefinitions = new List<TestDefinitionSO>();
        
        [Header("Database")]
        [SerializeField] private Dictionary<string, PatientTests> patientDatabase = new Dictionary<string, PatientTests>();
        [SerializeField] private int lastTestInstanceId = 1000;
        
        // Events
        // public static event Action<TestInstance> OnTestCreated;
        // public static event Action<TestInstance> OnTestCompleted;
        // public static event Action<TestInstance> OnTestUpdated;
        public static event Action<int> OnTestDeleted;
        
        public event Action<List<TestInstance>> OnPatientTestsReceived;

        private void Awake()
        {
            InstanceHandler.RegisterInstance(this);
            InitializeDatabase();
        }
         private static void GetInstance() 
        {
            //This will fail if the manager isn't registered
            InstanceHandler.GetInstance<LabTestDatabase>().Success();
        }

        private static void  TryGetInstance() 
        {
            //This will only run if we get the manager. Potentially invert the if statement and log and error if you don't get it
            if(InstanceHandler.TryGetInstance(out LabTestDatabase manager))
                manager.Success();
        }

        private void Success() 
        {
            Debug.Log("Now we're logging from the LabTestDatabase instance!", this);
        }

        private void InitializeDatabase()
        {
            if (patientDatabase == null)
                patientDatabase = new Dictionary<string, PatientTests>();

            // Editor'de test tanımlarını yükle
#if UNITY_EDITOR
            LoadAllTestDefinitions();
#endif
        }

        #region Test Definition Management

        // Tüm test tanımlarını Resources'dan yükle
        public void LoadAllTestDefinitions()
        {
            availableTestDefinitions.Clear();
            TestDefinitionSO[] definitions = Resources.LoadAll<TestDefinitionSO>("TestDefinitions");
            availableTestDefinitions.AddRange(definitions);
            Debug.Log($"Loaded {availableTestDefinitions.Count} test definitions");
        }

        // Test tanımı ekle
        public void RegisterTestDefinition(TestDefinitionSO definition)
        {
            if (definition != null && !availableTestDefinitions.Contains(definition))
            {
                availableTestDefinitions.Add(definition);
            }
        }

        // Test tanımını ID ile getir
        public TestDefinitionSO GetTestDefinition(string testDefinitionId)
        {
            return availableTestDefinitions.Find(td => td.testID == testDefinitionId);
        }

        // Tüm test tanımlarını getir
        public List<TestDefinitionSO> GetAllTestDefinitions()
        {
            return new List<TestDefinitionSO>(availableTestDefinitions);
        }

        // Kategoriye göre test tanımlarını getir
        public List<TestDefinitionSO> GetTestDefinitionsByCategory(TestCategory category)
        {
            return availableTestDefinitions.Where(td => td.category == category).ToList();
        }

        #endregion

        #region Test Instance Management

        // Yeni test oluştur
        public TestInstance CreateTest(string patientId, string testDefinitionId)
        {
            // Test tanımının var olduğunu kontrol et
            TestDefinitionSO definition = GetTestDefinition(testDefinitionId);
            if (definition == null)
            {
                Debug.LogError($"Test definition not found: {testDefinitionId}");
                return null;
            }

            // Yeni test instance oluştur
            int newTestId = ++lastTestInstanceId;
            TestInstance newTest = new TestInstance(newTestId, testDefinitionId, patientId);
            
            // Varsayılan parametre değerlerini ayarla
            foreach (var param in definition.parameters)
            {
                newTest.SetParameterResult(param.parameterName, param.defaultValue);
            }

            // Hastanın test koleksiyonuna ekle
            if (!patientDatabase.ContainsKey(patientId))
            {
                patientDatabase[patientId] = new PatientTests(patientId);
            }
            
            patientDatabase[patientId].AddTest(newTest);
            
            //OnTestCreated?.Invoke(newTest);
            
            Debug.Log($"Created test {testDefinitionId} for patient {patientId} with ID: {newTestId}");
            return newTest;
        }

        // Birden fazla test oluştur
        public List<TestInstance> CreateMultipleTests(string patientId, List<string> testDefinitionIds)
        {
            List<TestInstance> createdTests = new List<TestInstance>();
            
            foreach (string defId in testDefinitionIds)
            {
                TestInstance test = CreateTest(patientId, defId);
                if (test != null)
                {
                    createdTests.Add(test);
                }
            }
            
            return createdTests;
        }

        // Test instance'ı ID ile getir
        public TestInstance GetTestById(int instanceTestId)
        {
            foreach (var patientTests in patientDatabase.Values)
            {
                TestInstance test = patientTests.GetTest(instanceTestId);
                if (test != null)
                    return test;
            }
            return null;
        }

        // Hastanın tüm testlerini getir
        public List<TestInstance> GetPatientTests(string patientId)
        {
            if (patientDatabase.ContainsKey(patientId))
            {
                return new List<TestInstance>(patientDatabase[patientId].tests);
            }
            return new List<TestInstance>();
        }

        // Hastanın belirli durumdaki testlerini getir
        public List<TestInstance> GetPatientTestsByStatus(string patientId, TestStatus status)
        {
            if (patientDatabase.ContainsKey(patientId))
            {
                return patientDatabase[patientId].GetTestsByStatus(status);
            }
            return new List<TestInstance>();
        }

        // Test sonucu güncelle
        public bool UpdateTestResult(int instanceTestId, string parameterName, string value)
        {
            TestInstance test = GetTestById(instanceTestId);
            if (test != null)
            {
                test.SetParameterResult(parameterName, value);
                //OnTestUpdated?.Invoke(test);
                return true;
            }
            return false;
        }

        // Birden fazla test sonucu güncelle
        public bool UpdateMultipleTestResults(int instanceTestId, Dictionary<string, string> results)
        {
            TestInstance test = GetTestById(instanceTestId);
            if (test != null)
            {
                foreach (var kvp in results)
                {
                    test.SetParameterResult(kvp.Key, kvp.Value);
                }
                //OnTestUpdated?.Invoke(test);
                return true;
            }
            return false;
        }

        // Testi tamamla
        public bool CompleteTest(int instanceTestId)
        {
            TestInstance test = GetTestById(instanceTestId);
            if (test != null)
            {
                test.CompleteTest();
                //OnTestCompleted?.Invoke(test);
                return true;
            }
            return false;
        }

        // Test sil
        public bool DeleteTest(int instanceTestId)
        {
            foreach (var patientTests in patientDatabase.Values)
            {
                if (patientTests.RemoveTest(instanceTestId))
                {
                    OnTestDeleted?.Invoke(instanceTestId);
                    Debug.Log($"Deleted test with ID: {instanceTestId}");
                    return true;
                }
            }
            return false;
        }

        // Hastanın tüm testlerini sil
        public bool DeleteAllPatientTests(string patientId)
        {
            if (patientDatabase.ContainsKey(patientId))
            {
                var tests = patientDatabase[patientId].tests.ToList();
                foreach (var test in tests)
                {
                    OnTestDeleted?.Invoke(test.instanceTestId);
                }
                patientDatabase[patientId].tests.Clear();
                return true;
            }
            return false;
        }

        #endregion

        #region Data Persistence

        // Veritabanını JSON'a kaydet
        public void SaveDatabase(string filePath = null)
        {
            if (!isServer) return;
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = Application.persistentDataPath + "/labTestDatabase.json";
                }

                var saveData = new DatabaseSaveData
                {
                    lastTestInstanceId = lastTestInstanceId,
                    patients = patientDatabase
                };

                string json = JsonConvert.SerializeObject(saveData, Formatting.Indented);
                System.IO.File.WriteAllText(filePath, json);

                Debug.Log($"Database saved to: {filePath}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save database: {e.Message}");
            }
        }

        // JSON'dan veritabanını yükle
        public void LoadDatabase(string filePath = null)
        {
            if (!isServer) return;
            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = Application.persistentDataPath + "/labTestDatabase.json";
                }

                if (System.IO.File.Exists(filePath))
                {
                    string json = System.IO.File.ReadAllText(filePath);
                    var saveData = JsonConvert.DeserializeObject<DatabaseSaveData>(json);

                    if (saveData != null)
                    {
                        lastTestInstanceId = saveData.lastTestInstanceId;
                        patientDatabase = saveData.patients ?? new Dictionary<string, PatientTests>();

                        Debug.Log($"Database loaded from: {filePath}");
                    }
                }
                else
                {
                    Debug.LogWarning($"Database file not found: {filePath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load database: {e.Message}");
            }
        }

        // JSON'u import et
        public void ImportFromJson(string jsonContent)
        {
            if (!isServer) return;
            try
            {
                var saveData = JsonConvert.DeserializeObject<DatabaseSaveData>(jsonContent);
                if (saveData != null)
                {
                    lastTestInstanceId = saveData.lastTestInstanceId;
                    patientDatabase = saveData.patients ?? new Dictionary<string, PatientTests>();
                    Debug.Log("Database imported successfully");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to import JSON: {e.Message}");
            }
        }

        #endregion

        #region Statistics and Queries

        // Toplam test sayısı
        public int GetTotalTestCount()
        {
            return patientDatabase.Values.Sum(pt => pt.tests.Count);
        }

        // Belirli durumdaki test sayısı
        public int GetTestCountByStatus(TestStatus status)
        {
            return patientDatabase.Values.Sum(pt => pt.GetTestsByStatus(status).Count);
        }

        // En çok kullanılan test türleri
        public Dictionary<string, int> GetTestUsageStatistics()
        {
            Dictionary<string, int> stats = new Dictionary<string, int>();
            
            foreach (var patientTests in patientDatabase.Values)
            {
                foreach (var test in patientTests.tests)
                {
                    if (stats.ContainsKey(test.testDefinitionId))
                        stats[test.testDefinitionId]++;
                    else
                        stats[test.testDefinitionId] = 1;
                }
            }
            
            return stats;
        }

        // Tarih aralığındaki testleri getir
        public List<TestInstance> GetTestsByDateRange(DateTime startDate, DateTime endDate)
        {
            List<TestInstance> tests = new List<TestInstance>();
            
            foreach (var patientTests in patientDatabase.Values)
            {
                tests.AddRange(patientTests.tests.Where(t => 
                    t.dateRequested >= startDate && t.dateRequested <= endDate));
            }
            
            return tests;
        }

        #endregion

        #region RPC Methods
        [ServerRpc]
        public void RequestCreateTestServerRpc(string patientId, string testDefinitionId, RPCInfo info = default)
        {
            CreateTest(patientId, testDefinitionId);
        }
        [ServerRpc(requireOwnership: false)]
        public void RequestGetPatientTestsServerRpc(string patientId, RPCInfo info = default)
        {
            List<TestInstance> testResultst = GetPatientTests(patientId);
            NotifyClientGetPatientsRpc(info.sender, testResultst);
        }

        [TargetRpc]
        public void NotifyClientGetPatientsRpc(PlayerID target, List<TestInstance> tests)

        {
            OnPatientTestsReceived?.Invoke(tests);
        }



        #endregion


        // Kayıt verisi sınıfı
        [System.Serializable]
        private class DatabaseSaveData
        {
            public int lastTestInstanceId;
            public Dictionary<string, PatientTests> patients;
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
                SaveDatabase();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
                SaveDatabase();
        }

        private void OnDestroy()
        {
            SaveDatabase();
            InstanceHandler.UnregisterInstance<LabTestDatabase>();
        }
    }
}