using System;
using System.Collections.Generic;
using UnityEngine;

// Gender enum definition
public enum Gender
{
    Male,
    Female,
}

[Serializable]
public class PatientData
{
    public int patientId;
    public string patientName;
    public int  age;
    public Gender gender;
    public List<int> assignedTests = new List<int>(); // IDs of tests assigned to this patient

    public PatientData(int patientId, string patientName, int age, Gender gender)
    {
        this.patientId = patientId;
        this.patientName = patientName;
        this.age = age;
        this.gender = gender;
    }
}
