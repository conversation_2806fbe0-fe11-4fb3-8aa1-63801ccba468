// using UnityEngine;

// namespace Labsim
// {
//     [System.Serializable] // Inspector'da TestDefinitionSO içinde görünebilmesi için
//     public class ParameterDefinition
//     {
//         public string parameterName;
//         public FinalParameterDataType dataType;

//         [Tooltip("Bu parametre için bir referans aralığı var mı?")]
//         public bool hasReferenceRange = true;
//         [Tooltip("Sayısal parametreler için minimum referans değeri.")]
//         public float referenceMin;
//         [Tooltip("Sayısal parametreler için maksimum referans değeri.")]
//         public float referenceMax;

//          [Tooltip("Metinsel veya özel referans aralığı (örn: 'Negatif', 'Normal', '< 5.0')")]
//         public string referenceText; // Örn: "Negatif", "0 - 5 U/L", "< 1.1"


//         public string unit;
//     }
// }