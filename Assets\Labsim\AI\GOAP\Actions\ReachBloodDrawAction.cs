using CrashKonijn.Agent.Core;
using CrashKonijn.Agent.Runtime;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Behaviours;
using UnityEngine;

namespace Labsim.AI.GOAP.Actions
{
    [GoapId("ReachBloodDraw-acd42e58-b7f1-482d-9f87-4f5b8a2e1d36")]
    public class ReachBloodDrawAction : GoapActionBase<ReachBloodDrawAction.Data>
    {

        public override void Start(IMonoAgent agent, Data data)
        {
            // Validate target exists
            if (data.Target == null)
            {
                Debug.LogWarning("ReachBloodDrawAction started with null target");
                return;
            }

        }

        
       

        public override IActionRunState Perform(IMonoAgent agent, Data data, IActionContext context)
        {
            // Handle invalid target
            if (data.Target == null)
            {
                Debug.LogWarning("ReachBloodDrawAction has null target");
                return ActionRunState.Stop;
            }


            if (context.IsInRange)
            {
                
            }
            
            return ActionRunState.Continue;
        }

        public override void Complete(IMonoAgent agent, Data data)
        {
            if (data.patient != null)
            {
                Debug.Log("ReachBloodDrawAction Complete");
            }
        }

        public class Data : IActionData
        {
            public ITarget Target { get; set; }
            
            [GetComponent]
            public Patient patient { get; set; }
        }
    }
}
