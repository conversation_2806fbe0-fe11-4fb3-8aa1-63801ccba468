using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using PurrNet;

public class PlayerInteractionUIController : NetworkBehaviour
{
    [SerializeField] private UIDocument m_UIDocument;
    [SerializeField] private string m_InteractionText = "INTERACT";


    
    private VisualElement m_InteractionContainer;
    private VisualElement m_InteractionPrompt;
    private Label m_InteractLabel;
    private Label m_KeyLabel;
    private bool m_IsVisible = false;
    
    private void Awake()
    {
        if (m_UIDocument == null)
            m_UIDocument = GetComponent<UIDocument>();
    }
    

    protected override void OnSpawned()
    {
        base.OnSpawned();
        m_UIDocument.enabled = isOwner;
        enabled = isOwner;
        if (!isOwner) return;
        PlayerInteractor.OnInteractionHoverEnter += HandleInteractionHoverEnter;
        PlayerInteractor.OnInteractionHoverExit += HandleInteractionHoverExit;
        PlayerInteractor.OnInteractionPerformed += HandleInteractionPerformed;
        InitializeUI();
    }

    protected override void OnDespawned()
    {
        base.OnDespawned();
        PlayerInteractor.OnInteractionHoverEnter -= HandleInteractionHoverEnter;
        PlayerInteractor.OnInteractionHoverExit -= HandleInteractionHoverExit;
        PlayerInteractor.OnInteractionPerformed -= HandleInteractionPerformed;
    }

    private void HandleInteractionPerformed(IInteractable interactable)
    {
        HideInteractionPrompt();
    }

    private void HandleInteractionHoverEnter(IInteractable interactable, string interactionKey)
    {
        if (!interactable.CanInteract)
        {
            UpdateInteractionPrompt(interactable.InteractionBlockReason, "!");
            return;
        }
        UpdateInteractionPrompt(interactable.InteractionPrompt, interactionKey);
    }

    private void HandleInteractionHoverExit(IInteractable interactable)
    {
        HideInteractionPrompt();
    }
    
    
    private void InitializeUI()
    {
        if (m_UIDocument?.rootVisualElement == null)
        {
            Debug.LogError("UIDocument or root visual element is null!");
            return;
        }

        var root = m_UIDocument.rootVisualElement;

        m_InteractionContainer = root.Q<VisualElement>("interaction-container");
        m_InteractionPrompt = root.Q<VisualElement>("interaction-prompt");
        m_InteractLabel = root.Q<Label>("interact-label");
        m_KeyLabel = root.Q<Label>("key-icon");

        if (m_InteractionContainer == null || m_InteractionPrompt == null || m_InteractLabel == null || m_KeyLabel == null)
        {
            Debug.LogError("Failed to find UI elements in PlayerInteractionUI!");
            return;
        }

        HideInteractionPrompt();
    }
    
    public void ShowInteractionPrompt()
    {
        ShowInteractionPrompt(m_InteractionText);
    }
    
    public void ShowInteractionPrompt(string _interactionText)
    {
        if (m_InteractionContainer == null || m_IsVisible) return;
        
        m_InteractLabel.text = _interactionText;
        m_InteractionContainer.AddToClassList("visible");
        m_InteractionPrompt.AddToClassList("fade-in");
        m_InteractionPrompt.RemoveFromClassList("fade-out");
        
        m_IsVisible = true;
    }
    
    public void HideInteractionPrompt()
    {
        if (m_InteractionContainer == null || !m_IsVisible) return;
        
        m_InteractionPrompt.AddToClassList("fade-out");
        m_InteractionPrompt.RemoveFromClassList("fade-in");
        
        StartCoroutine(HideAfterDelay());
        
        m_IsVisible = false;
    }
    
    private IEnumerator HideAfterDelay()
    {
        yield return new WaitForSeconds(0.3f);
        
        if (m_InteractionContainer != null)
        {
            m_InteractionContainer.RemoveFromClassList("visible");
        }
    }
    
    public void UpdateInteractionText(string _newText)
    {
        if (m_InteractLabel != null)
        {
            m_InteractLabel.text = _newText;
        }
    }
    
    public void UpdateKeyText(string _newKey)
    {
        if (m_KeyLabel != null)
        {
            m_KeyLabel.text = _newKey;
        }
    }
    
    public void ShowInteractionPrompt(string _interactionText, string _keyText)
    {
        if (m_InteractionContainer == null || m_IsVisible) return;
        
        m_InteractLabel.text = _interactionText;
        m_KeyLabel.text = _keyText;
        m_InteractionContainer.AddToClassList("visible");
        m_InteractionPrompt.AddToClassList("fade-in");
        m_InteractionPrompt.RemoveFromClassList("fade-out");
        
        m_IsVisible = true;
    }
    
    public void UpdateInteractionPrompt(string _interactionText, string _keyText)
    {
        if (m_InteractionContainer == null) return;
        
        if (!m_IsVisible)
        {
            ShowInteractionPrompt(_interactionText, _keyText);
        }
        else
        {
            m_InteractLabel.text = _interactionText;
            m_KeyLabel.text = _keyText;
        }
    }
    
    public bool IsVisible => m_IsVisible;
}
