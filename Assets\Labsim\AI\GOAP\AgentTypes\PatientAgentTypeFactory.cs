// using CrashKonijn.Goap.Core;
// using CrashKonijn.Goap.Runtime;
// using Labsim.AI.GOAP.Capabilities;
// using Labsim.AI.GOAP.Sensors;

// namespace Labsim.AI.GOAP.AgentTypes
// {
//     public class PatientAgentTypeFactory : AgentTypeFactoryBase
//     {
//         public override IAgentTypeConfig Create()
//         {
//             var factory = new AgentTypeBuilder("PatientAgent");
            
//             factory.AddCapability<IdleCapabilityFactory>();
//             factory.AddCapability<ReachPhlebotomyCapability>();
//             factory.AddCapability<ReachBloodDrawCapability>();

//             return factory.Build();
//         }
//     }
// }