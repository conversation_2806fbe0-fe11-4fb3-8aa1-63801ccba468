<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/Labsim/ThirdPersonController/Scripts/Barcode/TestBarcodeUI.uss?fileID=7433441132597879392&amp;guid=6a959f0567acd9f47aaaf455f93aa925&amp;type=3#TestBarcodeUI" />
    <ui:VisualElement name="TestBarcodeContainer" class="test-barcode-container">

        <ui:VisualElement name="BarcodeContent" class="barcode-content">
            <ui:VisualElement name="TestInfo" class="test-info">
                <ui:Label text="ID: 0000" name="TestIdLabel" class="test-id-label" />
                <ui:Label text="Test Name" name="TestNameLabel" class="test-name-label" />
            </ui:VisualElement>
            <ui:VisualElement name="BarcodeStripes" class="barcode-stripes">
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
                <ui:VisualElement class="barcode-stripe" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
