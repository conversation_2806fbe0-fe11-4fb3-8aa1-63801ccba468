using UnityEngine;
using PurrNet;
using Labsim;
using System;

public class PhlebotomyTerminalButton : NetworkBehaviour, ITerminalInteractable
{
    //Broadcast events
    public static Action OnTerminalButtonClicked;

    //Interaction Properties
    public string InteractionPrompt => "Button";

    public string InteractionBlockReason => "Button busy";

    public bool CanInteract => true;

    public GameObject GameObject => gameObject;


    //Interaction functions
    public void OnHoverEnter(TerminalInteractor interactor)
    {
    }

    public void OnHoverExit(TerminalInteractor interactor)
    { 
    }

    public void OnInteract(TerminalInteractor interactor)
    {
        if (!isOwner) return;
        OnTerminalButtonClicked?.Invoke();
    }
}
