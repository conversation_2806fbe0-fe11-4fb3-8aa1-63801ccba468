using UnityEngine;
using PurrNet;
using System;
using System.Collections.Generic;
using Labsim.Phlebotomy;
using System.Linq;

namespace Labsim
{
    public class PhlebotomyTerminal : NetworkBehaviour, ITerminalComponent
    {
        //Terminal Properties
        [SerializeField] private Transform terminalWaitAreaTransform;
        [SerializeField] public bool isPatientInArea = false;


        //Synced variables
        [SerializeField] private SyncVar<int> callablePatientId = new(2001);

        public ITerminalServiceProvider ServiceProvider { get; set; }

        public static event Action<int, Transform> OnPhlebotomyTerminalCall;
        public event Action<int> OnPatientScanned;

        void Start()
        {
            PhlebotomyTerminalButton.OnTerminalButtonClicked += HandleTerminalButtonClicked;
            ServiceProvider.GetService<PhlebotomyTerminalInteractor>().enabled = false;
            InitializeListeningEvents();
        }


        //Listening events
        void InitializeListeningEvents()
        {
            ServiceProvider.GetService<PhlebotomyTerminalInteractable>().OnTerminalEntered += HandleTerminalEntered;
            ServiceProvider.GetService<PhlebotomyTerminalInteractor>().OnTerminalInteractionPerformed += HandleInteractionPerformed;
        }

        //Listening events functions


        private void HandleTerminalEntered(IInteractor interactor)
        {
            ServiceProvider.GetService<PhlebotomyTerminalInteractor>().enabled = true;
        }
        private void HandleTerminalButtonClicked()
        {
            OnPhlebotomyTerminalCallServerRpc(callablePatientId.value, terminalWaitAreaTransform);
        }
        private void HandleInteractionPerformed(ITerminalInteractable interactable)
        {
            if (interactable.GameObject.TryGetComponent<PatientBracelet>(out var patientBracelet))
            {
                OnPatientScanned?.Invoke(patientBracelet.GetPatientId());
           }
        }

        //SERVER RPC's

        [ServerRpc]
        private void OnPhlebotomyTerminalCallServerRpc(int patientId, Transform terminal)
        {
            OnPhlebotomyTerminalCall?.Invoke(patientId, terminal);
        }

    }
}