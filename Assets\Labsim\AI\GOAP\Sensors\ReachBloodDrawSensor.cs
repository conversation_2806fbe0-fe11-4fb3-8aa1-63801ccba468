// using CrashKonijn.Agent.Core;
// using CrashKonijn.Goap.Runtime;
// using Labsim.AI.GOAP.Behaviours; // DataBehaviour için
// using UnityEngine;
// using UnityEngine.AI; // NavMesh için

// namespace Labsim.AI.GOAP.Sensors
// {
    
//     public class ReachBloodDrawSensor : MultiSensorBase
//     {
//         public ReachBloodDrawSensor()
//         {
//             // Add a world sensor to track if the agent is called to blood draw
//             this.AddLocalWorldSensor<IsCalledToBloodDraw>((agent, references) =>
//             {
//                 // Get a cached reference to the DataBehaviour on the agent
//                 var patient = references.GetCachedComponent<Patient>();
                
//                 // Return 1 if called to phlebotomy, 0 if not
//                 return patient.isBloodDraw ? 1 : 0;
//             });

//             // Add a target sensor to find the blood draw target location
//             this.AddLocalTargetSensor<BloodDrawTarget>((agent, references, target) =>
//             {
//                 // Get a cached reference to the DataBehaviour
//                 var patient = references.GetCachedComponent<Patient>();

                
//                 // If not called to phlebotomy or no target terminal, return null
//                 if (!patient.isBloodDraw || patient.bloodDrawLocation == null)
//                     return null;
                
//                 // If we already have a transform target, update it
//                 if (target is TransformTarget transformTarget)
//                     return transformTarget.SetTransform(patient.bloodDrawLocation);
                
//                 // Otherwise create a new transform target
//                 return new TransformTarget(patient.bloodDrawLocation);
//             });
//         }

//         public override void Created() { }

//         public override void Update() { }
//     }
// }
