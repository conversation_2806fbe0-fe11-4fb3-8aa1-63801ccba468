// #if UNITY_EDITOR
// using UnityEngine;
// using UnityEditor;
// using LabTestSystem;
// using System.Collections.Generic;

// // Test Definition ScriptableObject için özel inspector
// [CustomEditor(typeof(TestDefinitionSO))]
// public class TestDefinitionEditor : Editor
// {
//     private SerializedProperty testID;
//     private SerializedProperty testName;
//     private SerializedProperty displayName;
//     private SerializedProperty category;
//     private SerializedProperty testColor;
//     private SerializedProperty parameters;
//     private SerializedProperty description;
    
//     private bool showParameters = true;
    
//     private void OnEnable()
//     {
//         testID = serializedObject.FindProperty("testID");
//         testName = serializedObject.FindProperty("testName");
//         displayName = serializedObject.FindProperty("displayName");
//         category = serializedObject.FindProperty("category");
//         testColor = serializedObject.FindProperty("testColor");
//         parameters = serializedObject.FindProperty("parameters");
//         description = serializedObject.FindProperty("description");
//     }
    
//     public override void OnInspectorGUI()
//     {
//         serializedObject.Update();
        
//         TestDefinitionSO testDef = (TestDefinitionSO)target;
        
//         // Header
//         EditorGUILayout.Space();
//         EditorGUILayout.LabelField("Test Definition", EditorStyles.boldLabel);
//         EditorGUILayout.Space();
        
//         // Basic Information
//         EditorGUILayout.BeginVertical("box");
//         EditorGUILayout.LabelField("Basic Information", EditorStyles.boldLabel);
//         EditorGUILayout.PropertyField(testID);
//         EditorGUILayout.PropertyField(testName);
//         EditorGUILayout.PropertyField(displayName);
//         EditorGUILayout.PropertyField(category);
//         EditorGUILayout.PropertyField(testColor);
//         EditorGUILayout.EndVertical();
        
//         EditorGUILayout.Space();
        
//         // Parameters Section
//         EditorGUILayout.BeginVertical("box");
//         showParameters = EditorGUILayout.Foldout(showParameters, "Parameters", true);
        
//         if (showParameters)
//         {
//             EditorGUI.indentLevel++;
            
//             // Parameter count
//             EditorGUILayout.BeginHorizontal();
//             EditorGUILayout.LabelField($"Parameter Count: {testDef.parameters.Count}");
            
//             if (GUILayout.Button("Add Parameter", GUILayout.Width(100)))
//             {
//                 testDef.parameters.Add(new TestParameter());
//             }
//             EditorGUILayout.EndHorizontal();
            
//             // Parameter list
//             for (int i = 0; i < parameters.arraySize; i++)
//             {
//                 EditorGUILayout.BeginVertical("box");
//                 SerializedProperty param = parameters.GetArrayElementAtIndex(i);
                
//                 EditorGUILayout.BeginHorizontal();
//                 param.isExpanded = EditorGUILayout.Foldout(param.isExpanded, 
//                     param.FindPropertyRelative("parameterName").stringValue);
                
//                 if (GUILayout.Button("Remove", GUILayout.Width(60)))
//                 {
//                     parameters.DeleteArrayElementAtIndex(i);
//                     break;
//                 }
//                 EditorGUILayout.EndHorizontal();
                
//                 if (param.isExpanded)
//                 {
//                     EditorGUI.indentLevel++;
//                     EditorGUILayout.PropertyField(param.FindPropertyRelative("parameterName"));
//                     EditorGUILayout.PropertyField(param.FindPropertyRelative("displayName"));
//                     EditorGUILayout.PropertyField(param.FindPropertyRelative("dataType"));
//                     EditorGUILayout.PropertyField(param.FindPropertyRelative("unit"));
//                     EditorGUILayout.PropertyField(param.FindPropertyRelative("defaultValue"));
                    
//                     SerializedProperty hasRef = param.FindPropertyRelative("hasReferenceRange");
//                     EditorGUILayout.PropertyField(hasRef);
                    
//                     if (hasRef.boolValue)
//                     {
//                         EditorGUI.indentLevel++;
//                         EditorGUILayout.PropertyField(param.FindPropertyRelative("referenceMin"));
//                         EditorGUILayout.PropertyField(param.FindPropertyRelative("referenceMax"));
//                         EditorGUILayout.PropertyField(param.FindPropertyRelative("referenceText"));
//                         EditorGUI.indentLevel--;
//                     }
                    
//                     EditorGUI.indentLevel--;
//                 }
                
//                 EditorGUILayout.EndVertical();
//             }
            
//             EditorGUI.indentLevel--;
//         }
//         EditorGUILayout.EndVertical();
        
//         EditorGUILayout.Space();
        
//         // Additional Settings
//         EditorGUILayout.BeginVertical("box");
//         EditorGUILayout.LabelField("Additional Settings", EditorStyles.boldLabel);
//         EditorGUILayout.PropertyField(description);
//         EditorGUILayout.PropertyField(serializedObject.FindProperty("estimatedCompletionTime"));
//         EditorGUILayout.PropertyField(serializedObject.FindProperty("requiresSample"));
//         EditorGUILayout.PropertyField(serializedObject.FindProperty("sampleType"));
//         EditorGUILayout.EndVertical();
        
//         serializedObject.ApplyModifiedProperties();
        
//         // Validation
//         EditorGUILayout.Space();
//         if (!testDef.IsValid())
//         {
//             EditorGUILayout.HelpBox("Test definition is not valid. Please check ID, Name and Parameters.", 
//                 MessageType.Warning);
//         }
//     }
// }

// // Lab Test Database için Editor Window
// public class LabTestDatabaseWindow : EditorWindow
// {
//     private Vector2 scrollPosition;
//     private string searchPatientId = "";
//     private TestStatus filterStatus = TestStatus.Requested;
//     private bool showOnlyFiltered = false;
    
//     private GUIStyle headerStyle;
//     private GUIStyle boxStyle;
    
//     [MenuItem("Window/Lab Test System/Database Manager")]
//     public static void ShowWindow()
//     {
//         LabTestDatabaseWindow window = GetWindow<LabTestDatabaseWindow>("Lab Test Database");
//         window.minSize = new Vector2(600, 400);
//     }
    
//     private void OnEnable()
//     {
//         InitStyles();
//     }
    
//     private void InitStyles()
//     {
//         headerStyle = new GUIStyle();
//         headerStyle.fontSize = 14;
//         headerStyle.fontStyle = FontStyle.Bold;
//         headerStyle.normal.textColor = Color.white;
        
//         boxStyle = new GUIStyle();
//         boxStyle.padding = new RectOffset(10, 10, 10, 10);
//     }
    
//     private void OnGUI()
//     {
//         if (!Application.isPlaying)
//         {
//             EditorGUILayout.HelpBox("Lab Test Database is only available in Play Mode", MessageType.Info);
//             return;
//         }
        
//         if (LabTestDatabase.Instance == null)
//         {
//             EditorGUILayout.HelpBox("Lab Test Database not found", MessageType.Warning);
//             return;
//         }
        
//         scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
//         // Header
//         EditorGUILayout.Space();
//         EditorGUILayout.LabelField("Lab Test Database Manager", headerStyle);
//         EditorGUILayout.Space();
        
//         // Statistics
//         DrawStatistics();
        
//         EditorGUILayout.Space();
        
//         // Search and Filter
//         DrawSearchAndFilter();
        
//         EditorGUILayout.Space();
        
//         // Test List
//         DrawTestList();
        
//         EditorGUILayout.Space();
        
//         // Actions
//         DrawActions();
        
//         EditorGUILayout.EndScrollView();
//     }
    
//     private void DrawStatistics()
//     {
//         EditorGUILayout.BeginVertical("box");
//         EditorGUILayout.LabelField("Statistics", EditorStyles.boldLabel);
        
//         int totalTests = LabTestDatabase.Instance.GetTotalTestCount();
//         int completedTests = LabTestDatabase.Instance.GetTestCountByStatus(TestStatus.Completed);
//         int pendingTests = LabTestDatabase.Instance.GetTestCountByStatus(TestStatus.Requested);
//         int inProgressTests = LabTestDatabase.Instance.GetTestCountByStatus(TestStatus.InProgress);
        
//         EditorGUILayout.BeginHorizontal();
//         EditorGUILayout.LabelField($"Total Tests: {totalTests}");
//         EditorGUILayout.LabelField($"Completed: {completedTests}");
//         EditorGUILayout.LabelField($"Pending: {pendingTests}");
//         EditorGUILayout.LabelField($"In Progress: {inProgressTests}");
//         EditorGUILayout.EndHorizontal();
        
//         EditorGUILayout.EndVertical();
//     }
    
//     private void DrawSearchAndFilter()
//     {
//         EditorGUILayout.BeginVertical("box");
//         EditorGUILayout.LabelField("Search & Filter", EditorStyles.boldLabel);
        
//         EditorGUILayout.BeginHorizontal();
//         EditorGUILayout.LabelField("Patient ID:", GUILayout.Width(80));
//         searchPatientId = EditorGUILayout.TextField(searchPatientId);
        
//         if (GUILayout.Button("Search", GUILayout.Width(80)))
//         {
//             // Search action
//         }
//         EditorGUILayout.EndHorizontal();
        
//         EditorGUILayout.BeginHorizontal();
//         showOnlyFiltered = EditorGUILayout.Toggle("Filter by Status", showOnlyFiltered);
//         if (showOnlyFiltered)
//         {
//             filterStatus = (TestStatus)EditorGUILayout.EnumPopup(filterStatus);
//         }
//         EditorGUILayout.EndHorizontal();
        
//         EditorGUILayout.EndVertical();
//     }
    
//     private void DrawTestList()
//     {
//         EditorGUILayout.BeginVertical("box");
//         EditorGUILayout.LabelField("Recent Tests", EditorStyles.boldLabel);
        
//         if (!string.IsNullOrEmpty(searchPatientId))
//         {
//             var tests = LabTestDatabase.Instance.GetPatientTests(searchPatientId);
            
//             if (showOnlyFiltered)
//             {
//                 tests = tests.FindAll(t => t.status == filterStatus);
//             }
            
//             foreach (var test in tests)
//             {
//                 DrawTestItem(test);
//             }
            
//             if (tests.Count == 0)
//             {
//                 EditorGUILayout.LabelField("No tests found");
//             }
//         }
//         else
//         {
//             EditorGUILayout.LabelField("Enter a patient ID to search");
//         }
        
//         EditorGUILayout.EndVertical();
//     }
    
//     private void DrawTestItem(TestInstance test)
//     {
//         EditorGUILayout.BeginHorizontal("box");
        
//         TestDefinitionSO definition = LabTestDatabase.Instance.GetTestDefinition(test.testDefinitionId);
//         string testName = definition != null ? definition.displayName : test.testDefinitionId;
        
//         EditorGUILayout.LabelField($"ID: {test.instanceTestId}", GUILayout.Width(100));
//         EditorGUILayout.LabelField($"Test: {testName}", GUILayout.Width(150));
//         EditorGUILayout.LabelField($"Status: {test.status}", GUILayout.Width(100));
//         EditorGUILayout.LabelField($"Date: {test.dateRequested:yyyy-MM-dd HH:mm}");
        
//         if (GUILayout.Button("Complete", GUILayout.Width(70)))
//         {
//             LabTestDatabase.Instance.CompleteTest(test.instanceTestId);
//         }
        
//         if (GUILayout.Button("Delete", GUILayout.Width(60)))
//         {
//             if (EditorUtility.DisplayDialog("Delete Test", 
//                 $"Are you sure you want to delete test {test.instanceTestId}?", 
//                 "Yes", "No"))
//             {
//                 LabTestDatabase.Instance.DeleteTest(test.instanceTestId);
//             }
//         }
        
//         EditorGUILayout.EndHorizontal();
//     }
    
//     private void DrawActions()
//     {
//         EditorGUILayout.BeginVertical("box");
//         EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
        
//         EditorGUILayout.BeginHorizontal();
        
//         if (GUILayout.Button("Save Database"))
//         {
//             LabTestDatabase.Instance.SaveDatabase();
//             EditorUtility.DisplayDialog("Success", "Database saved successfully", "OK");
//         }
        
//         if (GUILayout.Button("Load Database"))
//         {
//             LabTestDatabase.Instance.LoadDatabase();
//             EditorUtility.DisplayDialog("Success", "Database loaded successfully", "OK");
//         }
        
//         if (GUILayout.Button("Clear All Data"))
//         {
//             if (EditorUtility.DisplayDialog("Clear Database", 
//                 "Are you sure you want to clear all data? This cannot be undone.", 
//                 "Yes", "No"))
//             {
//                 // Clear implementation would go here
//                 EditorUtility.DisplayDialog("Success", "Database cleared", "OK");
//             }
//         }
        
//         EditorGUILayout.EndHorizontal();
//         EditorGUILayout.EndVertical();
//     }
// }

// // Test Definition oluşturucu wizard
// public class TestDefinitionWizard : ScriptableWizard
// {
//     public string testID = "NEW_TEST";
//     public string testName = "New Test";
//     public TestCategory category = TestCategory.Hematology;
//     public Color testColor = Color.white;
    
//     [MenuItem("Assets/Create/Lab Test System/Test Definition Wizard")]
//     static void CreateWizard()
//     {
//         ScriptableWizard.DisplayWizard<TestDefinitionWizard>("Create Test Definition", "Create");
//     }
    
//     void OnWizardCreate()
//     {
//         TestDefinitionSO newTest = ScriptableObject.CreateInstance<TestDefinitionSO>();
//         newTest.testID = testID;
//         newTest.testName = testName;
//         newTest.displayName = testName;
//         newTest.category = category;
//         newTest.testColor = testColor;
        
//         string path = EditorUtility.SaveFilePanel("Save Test Definition", 
//             "Assets/Resources/TestDefinitions", 
//             testID, "asset");
            
//         if (!string.IsNullOrEmpty(path))
//         {
//             path = FileUtil.GetProjectRelativePath(path);
//             AssetDatabase.CreateAsset(newTest, path);
//             AssetDatabase.SaveAssets();
//             EditorUtility.FocusProjectWindow();
//             Selection.activeObject = newTest;
//         }
//     }
// }
// #endif