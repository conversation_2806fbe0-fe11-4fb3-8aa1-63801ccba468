using CrashKonijn.Agent.Core;
using CrashKonijn.Agent.Runtime;
using CrashKonijn.Goap.Runtime;
using Labsim.AI.GOAP.Behaviours;
using UnityEngine;

namespace Labsim.AI.GOAP.Actions
{
   
    public class IdleBloodDrawAction : GoapActionBase<IdleBloodDrawAction.Data>
    {

        public override void Start(IMonoAgent agent, Data data)
        {
            if (data.Target == null)
            {
                return;
            }

        }

        

        public override IActionRunState Perform(IMonoAgent agent, Data data, IActionContext context)
        {
       
            if (data.Target == null)
            {
                Debug.LogWarning(" has null target");
                return ActionRunState.Stop;
            }


            if (context.IsInRange)
            {
                
            }
            
            return ActionRunState.Continue;
        }

        public override void Complete(IMonoAgent agent, Data data)
        {
            if (data.patient != null)
            {
                Debug.Log(" Complete");
            }
        }

        public class Data : IActionData
        {
            public ITarget Target { get; set; }
            
            [GetComponent]
            public Patient patient { get; set; }
        }
    }
}