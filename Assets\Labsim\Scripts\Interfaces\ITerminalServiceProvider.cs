using UnityEngine;
using System.Collections.Generic;
using System;

public interface ITerminalServiceProvider
{
    ITerminalComponent[] TerminalComponents { get; }
    public T GetService<T>() where T : class;
    public void AddService<T>();
    public void AddService(object service);

    public void RemoveService(object service);

    public void RemoveService<T>();

    void InjectProviderToComponents(ITerminalServiceProvider provider);
}
