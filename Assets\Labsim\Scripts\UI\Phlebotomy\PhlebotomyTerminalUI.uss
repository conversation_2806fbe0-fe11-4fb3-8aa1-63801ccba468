.main-container {
    width: 100%;
    height: 100%;
    background-color: rgba(15, 15, 15, 0.95);
    flex-direction: column;
    border-color: rgba(0, 255, 47, 1);
    border-width: 0px;
}

.main-container.outline{
     border-width: 10px;
}

.header-section {
    background-color: rgba(0, 120, 215, 0.9);
    padding: 15px 30px;
    border-bottom-width: 3px;
    border-bottom-color: rgb(0, 100, 180);
    min-height: 60px;
    justify-content: center;
    align-items: center;
}

.header-label {
    font-size: 28px;
    color: white;
    -unity-text-align: middle-center;
    -unity-font-style: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.content-area {
    flex-direction: row;
    flex-grow: 1;
    padding: 20px;
}

.left-panel {
    width: 350px;
    flex-direction: column;
}

.right-panel {
    flex-grow: 1;
    flex-direction: column;
    min-width: 500px;
}

.section-header {
    font-size: 20px;
    color: white;
    -unity-font-style: bold;
    margin-bottom: 15px;
    border-bottom-width: 2px;
    border-bottom-color: rgba(0, 120, 215, 0.7);
    padding-bottom: 8px;
}

.patient-info-container {
    background-color: rgba(40, 40, 40, 0.9);
    padding: 20px;
    border-radius: 8px;
    border-width: 2px;
    border-color: rgba(0, 120, 215, 0.5);
}

.patient-id-label {
    font-size: 18px;
    color: rgb(255, 255, 255);
    -unity-font-style: bold;
    background-color: rgba(0, 120, 215, 0.3);
    padding: 10px;
    border-radius: 5px;
}

.patient-stats {
    margin-top: 10px;
}

.patient-status {
    font-size: 14px;
    color: rgb(150, 255, 150);
    -unity-font-style: italic;
}

.instructions-panel {
    background-color: rgba(50, 50, 50, 0.9);
    padding: 20px;
    border-radius: 8px;
    border-width: 2px;
    border-color: rgba(100, 100, 100, 0.5);
}

.instructions-text {
    font-size: 14px;
    color: rgb(200, 200, 200);
    white-space: normal;
}

.buttons-panel {
    background-color: rgba(50, 50, 50, 0.9);
    padding: 15px;
    border-radius: 8px;
    border-width: 2px;
    border-color: rgba(100, 100, 100, 0.5);
    align-items: center;
    justify-content: center;
}

.print-button {
    background-color: rgba(0, 120, 215, 0.8);
    color: white;
    font-size: 16px;
    -unity-font-style: bold;
    padding: 12px 24px;
    border-radius: 6px;
    border-width: 2px;
    border-color: rgb(0, 100, 180);
    min-width: 120px;
    min-height: 40px;
    transition-duration: 0.2s;
}

.print-button:hover {
    background-color: rgba(0, 140, 235, 0.9);
    border-color: rgb(0, 120, 200);
    scale: 1.05;
}

.print-button:active {
    background-color: rgba(0, 100, 180, 0.9);
    scale: 0.95;
}

.tests-container {
    background-color: rgba(30, 30, 30, 0.9);
    padding: 20px;
    border-radius: 8px;
    border-width: 2px;
    border-color: rgba(0, 120, 215, 0.5);
    flex-grow: 1;
}

.tests-scroll-view {
    flex-grow: 1;
    background-color: rgba(25, 25, 25, 0.8);
    border-radius: 5px;
    border-width: 1px;
    border-color: rgba(80, 80, 80, 0.7);
}

.tests-list {
    padding: 15px;
}

.test-item {
    background-color: rgba(50, 50, 50, 0.95);
    padding: 15px 20px;
    border-radius: 8px;
    border-width: 1px;
    border-color: rgba(120, 120, 120, 0.7);
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    min-height: 70px;
    transition-duration: 0.2s;
}

.test-color-box {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border-width: 1px;
    border-color: rgba(255, 255, 255, 0.3);
    margin-right: 15px;
    flex-shrink: 0;
}

.test-item:hover {
    background-color: rgba(60, 60, 60, 0.95);
    border-color: rgba(0, 120, 215, 0.8);
}

.test-name {
    font-size: 18px;
    color: white;
    -unity-font-style: bold;
    flex-grow: 1;
    margin-right: 20px;
}

.test-status {
    font-size: 16px;
    -unity-font-style: bold;
    padding: 8px 15px;
    border-radius: 20px;
    min-width: 120px;
    -unity-text-align: middle-center;
    margin-right: 15px;
}

.test-status-pending {
    background-color: rgba(255, 193, 7, 0.9);
    color: rgb(0, 0, 0);
}

.test-status-completed {
    background-color: rgba(40, 167, 69, 0.9);
    color: white;
}

.test-status-in-progress {
    background-color: rgba(0, 123, 255, 0.9);
    color: white;
}

.test-date {
    font-size: 12px;
    color: rgb(180, 180, 180);
    min-width: 80px;
    -unity-text-align: middle-right;
}

.no-tests-message {
    flex-grow: 1;
    justify-content: center;
    align-items: center;
    background-color: rgba(40, 40, 40, 0.7);
    border-radius: 8px;
    padding: 40px;
    display: none;
}

.no-tests-label {
    font-size: 16px;
    color: rgb(150, 150, 150);
    -unity-text-align: middle-center;
    white-space: normal;
}
