using UnityEngine;
using PurrNet;
using DG.Tweening;
using Labsim.Phlebotomy;
using Labsim;
using UnityEngine.Animations;
using UnityEngine.UIElements;

public class SampleTube : NetworkBehaviour// ITerminalInteractable , IBarcodeable , IDropable
{
    private PhlebotomyTerminalTubeSpawner originalSpawner;
    private SocketManager currentSocketManager;
    private GameObject originalPrefab;
    private bool isReturning = false;
    private bool isBeingTouched = false;
    
    private Renderer objectRenderer;
    private Material originalMaterial;
    private Material highlightMaterial;

    // [SerializeField] private NetworkVariable<int> testInstanceId = new NetworkVariable<int>();
    [SerializeField] private Color highlightColor = Color.lightGreen;

    public string TerminalInteractionPrompt => "Take Sample Tube";

    public bool IsBeingTouched { get => throw new System.NotImplementedException(); set => throw new System.NotImplementedException(); }

    // public override void OnNetworkSpawn()
    // {
    //     base.OnNetworkSpawn();
    //     InitializeMaterials();
    //     SetupColliderForTriggerDetection();
    // }
    
    private void SetupColliderForTriggerDetection()
    {
        Collider collider = GetComponent<Collider>();
        if (collider != null)
        {
            // Make sure collider is set as trigger for OnTriggerEnter/Exit
            collider.isTrigger = true;
            Debug.Log($"[SampleTube] Collider set as trigger for {gameObject.name}");
        }
        else
        {
            Debug.LogError($"[SampleTube] No collider found on {gameObject.name}");
        }
    }
    
    private void InitializeMaterials()
    {
        objectRenderer = GetComponent<Renderer>();
        if (objectRenderer != null)
        {
            originalMaterial = objectRenderer.material;
            
            // Highlight material oluştur
            highlightMaterial = new Material(originalMaterial);
            highlightMaterial.color = highlightColor;
        }
    }

    public void SetOriginalSpawner(PhlebotomyTerminalTubeSpawner spawner)
    {
        originalSpawner = spawner;
    }

    public void SetCurrentSocketManager(SocketManager socketManager)
    {
        currentSocketManager = socketManager;
    }

    public void SetOriginalPrefab(GameObject prefab)
    {
        originalPrefab = prefab;
    }
    // public void TerminalInteract(NetworkObject interactorObject)
    // {
    //     if (isReturning || !IsServer) return;
    //     if (interactorObject.TryGetComponent<PhlebotomyTerminal>(out var phlebotomyTerminal))
    //     {
    //         ReturnToSpawnerServerRPC();
    //     }

        
    // }

    // [ServerRpc(RequireOwnership = false)]
    // private void ReturnToSpawnerServerRPC()
    // {
    //     if (isReturning) return;
        
    //     isReturning = true;
        
    //     if (currentSocketManager != null)
    //     {
    //         currentSocketManager.DetachFromSocket(gameObject);
    //     }

    //     if (originalSpawner != null)
    //     {
    //         Vector3 spawnerPosition = originalSpawner.transform.position;
            
    //         transform.DOMove(spawnerPosition, 1f)
    //             .SetEase(Ease.InCubic)
    //             .OnComplete(() => {
    //                 if (NetworkObject != null && NetworkObject.IsSpawned)
    //                 {
    //                     NetworkObject.Despawn();
    //                     if (originalPrefab != null)
    //                     {
    //                         NetworkObjectPool.Singleton.ReturnNetworkObject(NetworkObject, originalPrefab);
    //                     }
    //                 }
    //             });
    //     }
    //     else
    //     {
    //         if (NetworkObject != null && NetworkObject.IsSpawned)
    //         {
    //             NetworkObject.Despawn();
    //             if (originalPrefab != null)
    //             {
    //                 NetworkObjectPool.Singleton.ReturnNetworkObject(NetworkObject, originalPrefab);
    //             }
    //         }
    //     }
    // }

    // public void SetBarcode(int testId)
    // {
    //     testInstanceId.Value = testId;
    // }

   


    private void Update()
    {
    }
    
   
    
   
    void OnTriggerEnter(Collider other)
    {  
        if (objectRenderer != null && highlightMaterial != null)
        {
            objectRenderer.material = highlightMaterial;
        }
        
    }
    
    void OnTriggerExit(Collider other)
    {

        if (objectRenderer != null && originalMaterial != null)
        {
            objectRenderer.material = originalMaterial;
        }
        
    }

    // public void OnDrop(IDragable dragableObject)
    // {
    //     if (dragableObject is TestBarcode testBarcode && IsServer)
    //     {
    //         // Transfer test instance ID from TestBarcode to SampleTube
    //         testInstanceId.Value = testBarcode.GetTestInstanceId();
    //         Debug.Log($"[SampleTube] Assigned test ID {testInstanceId.Value} to SampleTube");
    //     }
    // }
    
    public void ResetHighlight()
    {
        if (objectRenderer != null && originalMaterial != null)
        {
            objectRenderer.material = originalMaterial;
        }
    }
}
