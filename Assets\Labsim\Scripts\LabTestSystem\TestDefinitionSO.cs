using UnityEngine;
using System.Collections.Generic;
using System;

namespace LabTestSystem
{
    // Test kategorileri enum
    public enum TestCategory
    {
        Hematology,
        Biochemistry,
        Microbiology,
        Coagulation,
        Urinalysis,
        Immunology,
        Other
    }

    // Parametre veri tipleri
    public enum ParameterDataType
    {
        Float,
        Integer,
        String,
        Boolean
    }

    // Test parametre tanımı
    [System.Serializable]
    public class TestParameter
    {
        public string parameterName;
        public string displayName;
        public ParameterDataType dataType = ParameterDataType.Float;
        public string unit;
        
        // Referans değerleri
        public bool hasReferenceRange = false;
        public float referenceMin;
        public float referenceMax;
        public string referenceText;
        
        // Varsayılan değer
        public string defaultValue = "0";
    }

    // Test tanım ScriptableObject
    [CreateAssetMenu(fileName = "New Test Definition", menuName = "Lab Test System/Test Definition")]
    public class TestDefinitionSO : ScriptableObject
    {
        [Header("Test Information")]
        public string testID; // Benzersiz test tanım ID'si (örn: "HEMOGRAM", "COAGULATION")
        public string testName;
        public string displayName;
        public TestCategory category;
        
        [Header("Visual Settings")]
        public Color testColor = Color.white;
        
        [Header("Parameters")]
        public List<TestParameter> parameters = new List<TestParameter>();
        
        [Header("Additional Settings")]
        [TextArea(3, 5)]
        public string description;
        public float estimatedCompletionTime = 60f; // Dakika cinsinden
        public bool requiresSample = true;
        public string sampleType; // Kan, İdrar, vs.
        
        // Yardımcı metod: Parametre ismine göre parametre bilgisi döndür
        public TestParameter GetParameter(string parameterName)
        {
            return parameters.Find(p => p.parameterName == parameterName);
        }
        
        // Yardımcı metod: Test tanımının geçerliliğini kontrol et
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(testID) && 
                   !string.IsNullOrEmpty(testName) && 
                   parameters != null && 
                   parameters.Count > 0;
        }
        
#if UNITY_EDITOR
        private void OnValidate()
        {
            // Editor'de testID otomatik olarak dosya adından alınsın
            if (string.IsNullOrEmpty(testID))
            {
                testID = name.ToUpper().Replace(" ", "_");
            }
            
            if (string.IsNullOrEmpty(displayName))
            {
                displayName = testName;
            }
        }
#endif
    }
}