.interaction-container {
    position: absolute;
    right: 53%;
    top: 50%;
    translate: 0 -50%;
    width: auto;
    height: auto;
    display: none;
}

.interaction-container.visible {
    display: flex;
}

.interaction-prompt {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    background-color: rgba(135, 135, 135, 0.8);
    border-radius: 8px;
    padding: 8px 8px;
}

.key-icon {
    width: 28px;
    height: 28px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    border-width: 1px;
    border-color: rgb(200, 200, 200);
    margin-right: 8px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    color: rgb(50, 50, 50);
    font-size: 14px;
    -unity-font-style: bold;
    -unity-text-align: middle-center;
}

.interact-label {
    color: rgb(255, 255, 255);
    font-size: 16px;
    -unity-font-style: bold;
    white-space: nowrap;
    margin: 0;
    padding: 0;
}

/* Animation classes */
.interaction-prompt.fade-in {
    transition-property: opacity;
    transition-duration: 0.3s;
    transition-timing-function: ease-in-out;
    opacity: 1;
}

.interaction-prompt.fade-out {
    transition-property: opacity;
    transition-duration: 0.3s;
    transition-timing-function: ease-in-out;
    opacity: 0;
}