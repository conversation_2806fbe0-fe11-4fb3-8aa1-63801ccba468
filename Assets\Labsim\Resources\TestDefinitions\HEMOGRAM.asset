%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b8a495defe3b07c4eae567f445d9d0f3, type: 3}
  m_Name: HEMOGRAM
  m_EditorClassIdentifier: Assembly-CSharp::LabTestSystem.TestDefinitionSO
  testID: HEMOGRAM
  testName: HEMOGRAM
  displayName: Hemogram
  category: 0
  testColor: {r: 0.44486028, g: 0, b: 0.7924528, a: 1}
  parameters:
  - parameterName: WBC
    displayName: WBC
    dataType: 0
    unit: 
    hasReferenceRange: 0
    referenceMin: 0
    referenceMax: 0
    referenceText: 
    defaultValue: 0
  - parameterName: HGB
    displayName: HGB
    dataType: 0
    unit: 
    hasReferenceRange: 0
    referenceMin: 0
    referenceMax: 0
    referenceText: 
    defaultValue: 0
  - parameterName: RGB
    displayName: RGB
    dataType: 0
    unit: 
    hasReferenceRange: 0
    referenceMin: 0
    referenceMax: 0
    referenceText: 
    defaultValue: 0
  description: 
  estimatedCompletionTime: 60
  requiresSample: 1
  sampleType: 
