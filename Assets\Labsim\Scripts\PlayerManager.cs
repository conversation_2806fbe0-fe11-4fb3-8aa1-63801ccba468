using UnityEngine;
using PurrNet;
using System.Collections.Generic;
using System;
using System.Linq;

public class PlayerManager : NetworkBehaviour
{
    public List<PlayerID> onlinePlayers = new List<PlayerID>();

    public static PlayerManager Singleton { get; private set; }
    
    public static Action<PlayerID> OnPlayerJoined;
    public static Action<PlayerID> OnPlayerLeft;

    protected override void OnSpawned()
    {
        base.OnSpawned();

        if (Singleton == null)
        {
            Singleton = this;
            networkManager.onPlayerJoined += HandlePlayerJoined;
            networkManager.onPlayerLeft += HandlePlayerLeft;
        }
        else if (Singleton != this)
        {
            Destroy(gameObject);
            return;
        }
    }

    protected override void OnDespawned()
    {
        base.OnDespawned();
        
        if (Singleton == this)
        {
            Singleton = null;
            networkManager.onPlayerJoined -= HandlePlayerJoined;
            networkManager.onPlayerLeft -= HandlePlayerLeft;
        }
    }

    private void HandlePlayerJoined(PlayerID player, bool isReconnect, bool asServer)
    {
        if (!asServer) return;
        onlinePlayers.Add(player);
        OnPlayerJoined?.Invoke(player);
    }

    private void HandlePlayerLeft(PlayerID player, bool asServer)
    {
        if (!asServer) return;
        onlinePlayers.Remove(player);
        OnPlayerLeft?.Invoke(player);
        //Debug.Log("Player left: " + player);
    }
    
  
}
