using System;
using Unity.Behavior;
using UnityEngine;
using Action = Unity.Behavior.Action;
using Unity.Properties;
using UnityEngine.Animations.Rigging;

[Serializable, GeneratePropertyBag]
[NodeDescription(name: "PhlebotomyTerminalIdle", story: "<PERSON><PERSON> waits idle near by phlebotomy terminal.", category: "Action", id: "2c654208964ec44f395d07c4822a4a24")]
public partial class PhlebotomyTerminalIdleAction : Action
{

    [SerializeReference] public BlackboardVariable<GameObject> Agent;

    RigBuilder rigBuilder;

    protected override Status OnStart()
    {
        rigBuilder = Agent.Value.GetComponent<RigBuilder>();
        rigBuilder.layers[0].rig.weight = 1;
        return Status.Running;
    }

    protected override Status OnUpdate()
    {
        Agent.Value.transform.eulerAngles = new Vector3(0f, -101.7f, 0f);
        return Status.Running;
    }

    protected override void OnEnd()
    {
    }
}

