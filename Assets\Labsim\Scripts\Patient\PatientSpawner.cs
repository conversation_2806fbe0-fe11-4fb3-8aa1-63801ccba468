using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Labsim;
using PurrNet;

public class PatientSpawner : NetworkBehaviour
{
    [Head<PERSON>("Spawning Settings")]
    [SerializeField]
    [Tooltip("The patient prefab to spawn. Must have a NetworkObject component and be registered in the NetworkObjectPool.")]
    private GameObject patientPrefab;


    [SerializeField]
    [Tooltip("Whether to spawn patients at random positions around the spawner")]
    private bool useRandomSpawnPosition = false;

    [SerializeField]
    [Tooltip("Radius around the spawner to randomly spawn patients (when random spawn is enabled)")]
    private float randomSpawnRadius = 3f;

    [SerializeField]
    [Tooltip("How often (in seconds) to spawn a new patient")]
    private float spawnFrequency = 10f;

    [SerializeField]
    [Tooltip("Maximum number of patients to spawn (0 = infinite)")]
    private int maxPatientCount = 0;

    [SerializeField]
    [Tooltip("Initial delay in seconds before first patient spawn")]
    private float initialDelay = 5f;

    private int spawnedPatientsCount = 0;


    

}