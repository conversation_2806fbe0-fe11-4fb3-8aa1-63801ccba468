// using UnityEngine;
// using System.Collections.Generic;

// namespace Labsim
// {
//     public class LabDatabaseManager : MonoBehaviour
// {
//     public static LabDatabaseManager Instance { get; private set; }

//     [Tooltip("Kullanılabilir tüm Test Tanımı ScriptableObject'leri buraya sürükleyin.")]
//     public List<TestDefinitionSO> availableTestDefinitions;

//     private ILabDatabaseService _databaseService;

//     void Awake()
//     {
//         if (Instance == null)
//         {
//             Instance = this;
//             DontDestroyOnLoad(gameObject); 

//             // Service'i initialize et - business logic delegate edildi
//             _databaseService = new LabDatabaseService(availableTestDefinitions);
//         }
//         else
//         {
//             Destroy(gameObject);
//         }
//     }

//     // Proxy methods - Service'e delegate et
//     public void LoadDatabase() => _databaseService.LoadDatabase();
//     public void SaveDatabase() => _databaseService.SaveDatabase();

//     public PatientProfile GetOrCreatePatient(string patientId) => 
//         _databaseService.GetOrCreatePatient(patientId);

//     public TestResultInstance AddTestToPatient(string patientId, string testDefinitionID) => 
//         _databaseService.AddTestToPatient(patientId, testDefinitionID);

//     public List<TestResultInstance> GetTestsForPatient(string patientId) => 
//         _databaseService.GetTestsForPatient(patientId);

//     public TestResultInstance GetSpecificTestForPatient(string patientId, int instanceTestId) => 
//         _databaseService.GetSpecificTestForPatient(patientId, instanceTestId);

//     public bool UpdateTestParameterValue(string patientId, int instanceTestId, string parameterName, object value) => 
//         _databaseService.UpdateTestParameterValue(patientId, instanceTestId, parameterName, value);

//     public bool UpdateTestStatus(string patientId, int instanceTestId, TestStatus newStatus) => 
//         _databaseService.UpdateTestStatus(patientId, instanceTestId, newStatus);

//     public List<string> GetAvailableTestDefinitionIDs() => 
//         _databaseService.GetAvailableTestDefinitionIDs();

//     public TestDefinitionSO GetTestDefinitionByID(string testDefinitionID_FK) => 
//         _databaseService.GetTestDefinitionByID(testDefinitionID_FK);

//     // Static facade methods for backward compatibility and ease of use
//     public static PatientProfile GetOrCreatePatientStatic(string patientId) => 
//         Instance.GetOrCreatePatient(patientId);

//     public static TestResultInstance AddTestToPatientStatic(string patientId, string testDefinitionID) => 
//         Instance.AddTestToPatient(patientId, testDefinitionID);

//     public static List<TestResultInstance> GetTestsForPatientStatic(string patientId) => 
//         Instance.GetTestsForPatient(patientId);

//     public static TestResultInstance GetSpecificTestForPatientStatic(string patientId, int instanceTestId) => 
//         Instance.GetSpecificTestForPatient(patientId, instanceTestId);

//     public static bool UpdateTestParameterValueStatic(string patientId, int instanceTestId, string parameterName, object value) => 
//         Instance.UpdateTestParameterValue(patientId, instanceTestId, parameterName, value);

//     public static bool UpdateTestStatusStatic(string patientId, int instanceTestId, TestStatus newStatus) => 
//         Instance.UpdateTestStatus(patientId, instanceTestId, newStatus);

//     public static List<string> GetAvailableTestDefinitionIDsStatic() => 
//         Instance.GetAvailableTestDefinitionIDs();

//     public static TestDefinitionSO GetTestDefinitionByIDStatic(string testDefinitionID_FK) => 
//         Instance.GetTestDefinitionByID(testDefinitionID_FK);

//     void OnApplicationQuit()
//     {
//         if (Instance == this)
//         {
//             SaveDatabase();
//         }
//     }
// }
// }