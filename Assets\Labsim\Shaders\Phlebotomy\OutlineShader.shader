Shader "Custom/OutlineURPShader"
{
    Properties
    {
        _Color ("Outline Color", Color) = (0, 1, 0, 1) // Dış hat rengi (Yeşil)
        _OutlineThickness ("Outline Thickness", Range(1.0, 1.2)) = 1.05 // Dış hat kalınlığı
    }

    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" }

        // Bu satır efektin kilit noktasıdır!
        // Normalde objelerin bize dönük olan ön yüzeyleri çizilir (Cull Back).
        // Biz burada "Ön yüzeyleri kırp/çizme" (Cull Front) diyoruz.
        // Bu sayede, biraz büyüttüğümüz modelin sadece içe bakan yüzeyleri çizilir ve
        // bu da bize bir "kabuk" (shell) efekti verir.
        Cull Front

        Pass
        {
            HLSLPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            // Değişiklik 1: Vertex verisine normal'i de ekledik (daha gelişmiş bir yöntem için).
            // Ancak şimdilik sadece pozisyonu kullanacağız.
            struct Attributes
            {
                float4 positionOS : POSITION;
                // float3 normalOS : NORMAL; // Bu satırı şimdilik kullanmıyoruz ama daha iyi bir yöntem için burada kalsın.
            };

            struct Varyings
            {
                float4 positionHCS : SV_POSITION;
            };
            
            // Değişiklik 2: Shader'ın kullanacağı değişkenleri CBUFFER içinde tanımlıyoruz.
            CBUFFER_START(UnityPerMaterial)
                half4 _Color;
                float _OutlineThickness;
            CBUFFER_END

            Varyings vert(Attributes IN)
            {
                Varyings OUT;
                
                // Değişiklik 3: Vertex'in pozisyonunu yerel koordinat sisteminde (object space)
                // _OutlineThickness değeri ile çarparak genişletiyoruz.
                // Bu, objeyi merkezinden dışarı doğru orantılı bir şekilde büyütür.
                IN.positionOS.xyz *= _OutlineThickness;

                OUT.positionHCS = TransformObjectToHClip(IN.positionOS.xyz);
                return OUT;
            }

            half4 frag(Varyings IN) : SV_Target
            {
                // Değişiklik 4: Fragment shader'ı çok basit. Herhangi bir hesaplama yapmadan
                // doğrudan bizim belirlediğimiz _Color (yeşil) değerini döndürüyor.
                // Texture veya başka bir şey kullanmıyoruz.
                return _Color;
            }
            ENDHLSL
        }
    }
}