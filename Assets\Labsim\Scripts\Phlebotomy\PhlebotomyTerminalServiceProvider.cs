using UnityEngine;
using System.Collections.Generic;
using System;
using Labsim;

[DefaultExecutionOrder(-200)] 
public class PhlebotomyTerminalServiceProvider : MonoBehaviour, ITerminalServiceProvider
{
    public ITerminalComponent[] TerminalComponents { get; private set; }

    public Dictionary<Type, object> Services = new Dictionary<Type, object>();

    void Awake()
    {
        TerminalComponents = GetComponentsInChildren<ITerminalComponent>();
        InitializeServices();  
        InjectProviderToComponents(this);
    }
    void InitializeServices()
    {
        AddService<PhlebotomyTerminal>();
        AddService<PhlebotomyTerminalUIController>();
        AddService<PhlebotomyTerminalCameraController>();
        AddService<PhlebotomyTerminalInteractable>();
        AddService<PhlebotomyScreenInteractable>();
        AddService<PhlebotomyTerminalInteractor>();
    }

    public T GetService<T>() where T : class
    {
        if (Services.TryGetValue(typeof(T), out var service))
        {
            return service as T;
        }
        return null;
    }

    public void AddService<T>()
    {
        if (Services.ContainsKey(typeof(T))) return;
        T service = GetComponentInChildren<T>();
        Services.Add(typeof(T), service);
    }

    public void AddService(object service)
    {
        Type serviceType = service.GetType();
        if (Services.ContainsKey(serviceType)) return;
        Services.Add(serviceType, service);
    }

    public void RemoveService<T>()
    {
        if (Services.ContainsKey(typeof(T))) Services.Remove(typeof(T));
    }

    public void RemoveService(object service)
    {
        Type serviceType = service.GetType();
        if (Services.ContainsKey(serviceType)) Services.Remove(serviceType);
    }

    public void InjectProviderToComponents(ITerminalServiceProvider provider)
    {
        if (TerminalComponents == null) Debug.LogError("There is no TerminalComponents");
        foreach (var component in TerminalComponents)
        {
            component.ServiceProvider = provider;
        }
    }
    
   
}
