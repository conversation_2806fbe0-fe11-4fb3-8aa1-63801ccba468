using UnityEngine;
using UnityEngine.Events;
using PurrNet;

public abstract class InteractableBase : NetworkBehaviour, IInteractable
{
    [<PERSON><PERSON>("Interaction Settings")]
    [SerializeField] protected string interactionPrompt = "Interact";

    [SerializeField] protected string interactionBlockReason = "Cannot interact";
    protected SyncVar<bool> canInteract = new(true);

    [Header("Events")]
    public UnityEvent<IInteractor> OnInteracted;
    public UnityEvent<IInteractor> OnHoverStarted;
    public UnityEvent<IInteractor> OnHoverEnded;

    // Properties
    public virtual string InteractionPrompt => interactionPrompt;

    public virtual string InteractionBlockReason => interactionBlockReason;
    public virtual bool CanInteract => canInteract.value && enabled;

    // State
    protected bool isHighlighted = false;

    protected virtual void Start()
    {
        if (GetComponent<Collider>() == null)
        {
            Debug.LogWarning($"[{gameObject.name}] No collider found. Adding box collider.");
            gameObject.AddComponent<BoxCollider>();
        }
        gameObject.layer = LayerMask.NameToLayer("Interactable");
        if (gameObject.layer == 0)
        {
            Debug.LogWarning("Interactable layer not found. Please create an 'Interactable' layer.");
        }
    }

    public virtual void OnInteract(IInteractor interactor)
    {
        if (!CanInteract) return;

        OnInteracted?.Invoke(interactor);
        HandleInteraction(interactor);
    }

    public virtual void OnHoverEnter(IInteractor interactor)
    {
        if (!isHighlighted)
        {
            isHighlighted = true;
            OnHoverStarted?.Invoke(interactor);
            HandleHoverEnter(interactor);
        }
    }

    public virtual void OnHoverExit(IInteractor interactor)
    {
        if (isHighlighted)
        {
            isHighlighted = false;
            OnHoverEnded?.Invoke(interactor);
            HandleHoverExit(interactor);
        }
    }

    protected abstract void HandleInteraction(IInteractor interactor);
    protected virtual void HandleHoverEnter(IInteractor interactor) { }
    protected virtual void HandleHoverExit(IInteractor interactor) { }
}