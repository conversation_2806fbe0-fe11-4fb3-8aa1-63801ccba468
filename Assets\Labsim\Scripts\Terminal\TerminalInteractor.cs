using UnityEngine;
using PurrNet;
using System;
using UnityEngine.InputSystem;


public abstract class TerminalInteractor : NetworkBehaviour, ITerminalComponent
{
    [Header("Ray Settings")]
    [SerializeField] private Transform rayOrigin;
    [SerializeField] private float rayDistance = 5f;
    [SerializeField] private LayerMask interactableLayer;

    //References
    public PlayerInputHandler playerInputHandler;
    public TerminalInteractableBase terminalInteractable;

    [SerializeField] private string m_InteractionKey = "";
    public string InteractionKey => m_InteractionKey;

    public ITerminalServiceProvider ServiceProvider { get ; set; }


    // State
    private ITerminalInteractable currentTarget;
    private ITerminalInteractable previousTarget;
    private bool previousCanInteract;

    // Events
    public  Action<ITerminalInteractable> OnTerminalTargetChanged;
    public  Action<ITerminalInteractable> OnTerminalInteractionPerformed;
    public  Action<ITerminalInteractable, string> OnTerminalInteractionHoverEnter;
    public  Action<ITerminalInteractable> OnTerminalInteractionHoverExit;

    private bool isRayOriginInitialized = false;


    private void Awake()
    {
        terminalInteractable = GetComponent<TerminalInteractableBase>();
    }

    protected override void OnSpawned()
    {
        base.OnSpawned();

        if (interactableLayer == 0)
        {
            interactableLayer = LayerMask.GetMask("TerminalInteractable");
            if (interactableLayer == 0)
            {
                Debug.LogError("Interactable layer not found! Please create an 'Interactable' layer.");
            }
        }
        terminalInteractable.OnTerminalExited += HandleTerminalExited;
        terminalInteractable.OnTerminalEntered += HandleTerminalInteracted;
    }

    void Update()
    {
        if (!isOwner) return;
        if (!isRayOriginInitialized)
        {
            InitializeRayOrigin();
            if (!isRayOriginInitialized) return;
        }

        PerformRaycast();
        CheckInteractionStateChange();
    }

    private void InitializeRayOrigin()
    {
        if (rayOrigin != null)
        {
            isRayOriginInitialized = true;
            return;
        }
        GameObject cameraObj = GameObject.FindGameObjectWithTag("PlayerMainCamera");
        if (cameraObj != null)
        {
            rayOrigin = cameraObj.transform;
            isRayOriginInitialized = true;
        }
    }

    private void PerformRaycast()
    {
        Ray ray = new Ray(rayOrigin.position, rayOrigin.forward);
        ITerminalInteractable detected = null;

        if (Physics.Raycast(ray, out RaycastHit hit, rayDistance, interactableLayer))
        {
            detected = hit.collider.GetComponent<ITerminalInteractable>();
        }

        UpdateTarget(detected);
    }



    private void UpdateTarget(ITerminalInteractable newTarget)
    {

        if (newTarget == previousTarget) return;


        if (previousTarget != null)
        {
            previousTarget.OnHoverExit(this);
            OnTerminalInteractionHoverExit?.Invoke(previousTarget);
        }


        if (newTarget != null)
        {
            newTarget.OnHoverEnter(this);
            OnTerminalTargetChanged?.Invoke(newTarget);

            OnTerminalInteractionHoverEnter?.Invoke(newTarget, InteractionKey);
        }

        currentTarget = newTarget;
        previousTarget = newTarget;
        previousCanInteract = newTarget?.CanInteract ?? false;
    }

    private void CheckInteractionStateChange()
    {
        if (currentTarget != null)
        {
            bool currentCanInteract = currentTarget.CanInteract;
            if (currentCanInteract != previousCanInteract)
            {
                previousCanInteract = currentCanInteract;


                OnTerminalInteractionHoverEnter?.Invoke(currentTarget, InteractionKey);
            }
        }
    }

    void HandleInteract(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            TryInteract();
        }
    }


    public void TryInteract()
    {
        if (currentTarget != null && currentTarget.CanInteract)
        {
            currentTarget.OnInteract(this);
            OnTerminalInteractionPerformed?.Invoke(currentTarget);
        }
    }

    protected override void OnDespawned()
    {
        base.OnDespawned();

        if (playerInputHandler != null && playerInputHandler.playerInput != null)
        {
            playerInputHandler.playerInput.Terminal.TerminalInteract.performed -= HandleInteract;
        }

        if (currentTarget != null)
        {
            currentTarget.OnHoverExit(this);
            currentTarget = null;
        }

        previousTarget = null;
    }

    void HandleTerminalInteracted(IInteractor interactor)
    {
        playerInputHandler = interactor.Transform.GetComponent<PlayerInputHandler>();
        playerInputHandler.playerInput.Terminal.TerminalInteract.performed += HandleInteract;
    }
    void HandleTerminalExited(IInteractor interactor)
    {
        playerInputHandler.playerInput.Terminal.TerminalInteract.performed -= HandleInteract;
        
        // Clean up hover state
        if (currentTarget != null)
        {
            currentTarget.OnHoverExit(this);
            currentTarget = null;
        }
        previousTarget = null;
    }
}
