using System;
using Labsim;
using UnityEngine;
using PurrNet;

public class PatientBracelet : NetworkBehaviour, ITerminalInteractable
{

    Patient patient;
    public Action<TerminalInteractor> OnBraceletInteract;

    public string InteractionPrompt => "bracelet";

    public string InteractionBlockReason => "block";

    public bool CanInteract => true;

    public GameObject GameObject => gameObject;

    private void Awake()
    {
        patient = GetComponentInParent<Patient>();
    }

    public void OnHoverEnter(TerminalInteractor interactor)
    {

    }

    public void OnHoverExit(TerminalInteractor interactor)
    {

    }

    public void OnInteract(TerminalInteractor interactor)
    {
        OnBraceletInteract?.Invoke(interactor);
    }
    
    public int GetPatientId()
    {
        return patient.patientId.value;
    }
}
