using System;
using Unity.Behavior;
using UnityEngine;
using Action = Unity.Behavior.Action;
using Unity.Properties;
using UnityEngine.AI;

[Serializable, GeneratePropertyBag]
[NodeDescription(name: "DetectTerminal", story: "Detect Terminal", category: "Action", id: "8b3c5dbbb9eca0ac49c931eaeca95340")]
public partial class DetectTerminalAction : Action
{
    [SerializeReference] public BlackboardVariable<GameObject> Agent;
    [SerializeReference] public BlackboardVariable<Transform> ActionTerminalTarget;
    [SerializeReference] public BlackboardVariable<bool> IsTerminalCalled;

    Patient patient;
    PatientTerminalSensor patientTerminalSensor;


    protected override Status OnStart()
    {
        if (Agent.Value == null)
        {
            Debug.LogError("DetectTerminalAction: Agent is null");
            return Status.Failure;
        }
        
        patientTerminalSensor = Agent.Value.GetComponent<PatientTerminalSensor>();
        patient = Agent.Value.GetComponent<Patient>();
        
        if (patientTerminalSensor == null)
        {
            Debug.LogError("DetectTerminalAction: PatientTerminalSensor component not found on Agent");
            return Status.Failure;
        }
        
        if (patient == null)
        {
            Debug.LogError("DetectTerminalAction: Patient component not found on Agent");
            return Status.Failure;
        }
        
        patientTerminalSensor.OnPhlebotomyTerminalCall += HandlePhlebotomyTerminalCall;
        return Status.Running;
    }
    void HandlePhlebotomyTerminalCall(int patientid, Transform terminal)
    {
        if (patient.patientId.value == patientid)
        {
            if (terminal != null)
            {
                ActionTerminalTarget.Value = terminal;
                IsTerminalCalled.Value = true;
            }
            else
            {
                Debug.LogError("Terminal call received, but the terminal Transform is NULL!");
            }
        }

        
    }

    protected override Status OnUpdate()
    {
        if (IsTerminalCalled.Value)
        {
            return Status.Success;
        }
        return Status.Running;
    }

    protected override void OnEnd()
    {
        if (patientTerminalSensor != null)
        {
            patientTerminalSensor.OnPhlebotomyTerminalCall -= HandlePhlebotomyTerminalCall;
        }
        
        if (IsTerminalCalled != null)
        {
            IsTerminalCalled.Value = false;
        }
    }
}

