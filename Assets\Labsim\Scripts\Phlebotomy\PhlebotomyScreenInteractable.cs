using UnityEngine;
using System;

public class PhlebotomyScreenInteractable : MonoBehaviour , ITerminalInteractable , ITerminalComponent
{
    public Action PhlebotomyScreenInteracted;

    public GameObject GameObject => gameObject;

    public string InteractionPrompt => "Interact Screen";

    public string InteractionBlockReason => "UI block";

    public bool CanInteract => true;

    public ITerminalServiceProvider ServiceProvider { get; set ; }

    public void OnHoverEnter(TerminalInteractor interactor)
    {
        ServiceProvider.GetService<PhlebotomyTerminalUIController>().SetMainContainerOutline(true);
    }

    public void OnHoverExit(TerminalInteractor interactor)
    {
        ServiceProvider.GetService<PhlebotomyTerminalUIController>().SetMainContainerOutline(false);
    }

    public void OnInteract(TerminalInteractor interactor)
    {
        PhlebotomyScreenInteracted?.Invoke();
    }

}
