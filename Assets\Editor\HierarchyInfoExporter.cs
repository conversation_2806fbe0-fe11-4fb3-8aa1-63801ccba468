using UnityEngine;
using UnityEditor;
using System.Text;

public class SimpleHierarchyExporter : EditorWindow
{
    private GameObject rootObjectForWindow;

    [MenuItem("Tools/Simple Hierarchy Exporter")]
    public static void ShowWindow()
    {
        GetWindow<SimpleHierarchyExporter>("Simple Hierarchy Exporter");
    }

    void OnGUI()
    {
        GUILayout.Label("Export Simple Hierarchy & Components", EditorStyles.boldLabel);
        rootObjectForWindow = (GameObject)EditorGUILayout.ObjectField("Root GameObject:", rootObjectForWindow, typeof(GameObject), true);

        if (GUILayout.Button("Export to Console & Clipboard"))
        {
            if (rootObjectForWindow != null)
            {
                PerformExport(rootObjectForWindow, "Window");
            }
            else
            {
                Debug.LogError("Please assign a Root GameObject in the window to export.");
            }
        }

        GUILayout.Space(10);
        GUILayout.Label("Right-click on a GameObject in Hierarchy to export it.", EditorStyles.centeredGreyMiniLabel);
    }

    private const string CONTEXT_MENU_PATH = "GameObject/Export Simple Hierarchy & Components";

    [MenuItem(CONTEXT_MENU_PATH, false, 11)] // Öncekiyle çakışmaması için priority'yi değiştirdim
    private static void ExportFromContextMenu()
    {
        GameObject selectedObject = Selection.activeGameObject;
        if (selectedObject == null)
        {
            Debug.LogWarning("No GameObject selected to export from context menu.");
            return;
        }
        PerformExport(selectedObject, "Context Menu");
    }

    [MenuItem(CONTEXT_MENU_PATH, true)]
    private static bool ValidateExportFromContextMenu()
    {
        return Selection.activeGameObject != null;
    }

    private static void PerformExport(GameObject rootObject, string source)
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine($"===== SIMPLE HIERARCHY & COMPONENTS (from {source}) =====");
        sb.AppendLine($"Starting from: {rootObject.name}\n");
        
        PrintSimpleHierarchyRecursive(rootObject.transform, 0, sb);
        
        string resultText = sb.ToString();
        Debug.Log(resultText);
        EditorGUIUtility.systemCopyBuffer = resultText;
        EditorUtility.DisplayDialog("Export Complete", $"Simple info for '{rootObject.name}' (from {source}) printed to console and copied to clipboard.", "OK");
    }

    private static void PrintSimpleHierarchyRecursive(Transform currentTransform, int indentLevel, StringBuilder sb)
    {
        // GameObject Adı
        sb.Append(new string(' ', indentLevel * 2)); // Girinti
        sb.Append($"- {currentTransform.name}");
        sb.AppendLine($" (Active: {currentTransform.gameObject.activeSelf}, Tag: {currentTransform.tag}, Layer: {LayerMask.LayerToName(currentTransform.gameObject.layer)})");

        // Component'ler
        Component[] components = currentTransform.GetComponents<Component>();
        foreach (Component component in components)
        {
            if (component == null) continue;

            sb.Append(new string(' ', (indentLevel + 1) * 2)); // Komponentler için bir girinti daha
            sb.AppendLine($"  L Component: {component.GetType().Name}");
        }
        sb.AppendLine(); // Objeler arası boşluk

        // Çocuklar için recursive çağrı
        foreach (Transform child in currentTransform)
        {
            PrintSimpleHierarchyRecursive(child, indentLevel + 1, sb);
        }
    }
}