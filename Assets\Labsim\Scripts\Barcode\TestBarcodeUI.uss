/* Test Barcode Container */
.test-barcode-container {
    width: 300px;
    height: 120px;
    background-color: rgb(255, 255, 255);
    border-color: rgb(0, 0, 0);
    border-width: 2px;
    border-radius: 5px;
    padding: 15px;
    justify-content: center;
    align-items: center;
}

/* Content */
.barcode-content {
    width: 100%;
    height: 100%;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

/* Test Info */
.test-info {
    width: 65%;
    height: 100%;
    justify-content: center;
    align-items: flex-start;
    padding-right: 15px;
}

.test-id-label {
    font-size: 18px;
    color: rgb(0, 0, 0);
    -unity-font-style: bold;
    margin-bottom: 8px;
    -unity-text-align: middle-left;
}

.test-name-label {
    font-size: 16px;
    color: rgb(30, 30, 30);
    -unity-font-style: bold;
    -unity-text-align: middle-left;
    white-space: normal;
}

/* Barcode Stripes */
.barcode-stripes {
    width: 35%;
    height: 80px;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
    background-color: rgb(240, 240, 240);
    border-color: rgb(200, 200, 200);
    border-width: 1px;
    padding: 2px;
}

.barcode-stripe {
    width: 8%;
    background-color: rgb(0, 0, 0);
    margin-left: 1px;
    margin-right: 1px;
}

/* Alternative stripe styles - you can manually assign these classes */
.barcode-stripe-white {
    width: 8%;
    background-color: rgb(255, 255, 255);
    margin-left: 1px;
    margin-right: 1px;
}

.barcode-stripe-thin {
    width: 6%;
    background-color: rgb(0, 0, 0);
    margin-left: 1px;
    margin-right: 1px;
}

.barcode-stripe-thick {
    width: 10%;
    background-color: rgb(0, 0, 0);
    margin-left: 1px;
    margin-right: 1px;
}
