using UnityEngine;
using PurrNet;
using Labsim;
using System;

public class PatientTerminalSensor : NetworkBehaviour
{
    public event Action<int, Transform> OnPhlebotomyTerminalCall;
    protected override void OnSpawned()
    {
        PhlebotomyTerminal.OnPhlebotomyTerminalCall += HandlePhlebotomyTerminalCall;
    }

    void HandlePhlebotomyTerminalCall(int patientId, Transform terminal)
    {
        OnPhlebotomyTerminalCall?.Invoke(patientId, terminal);
    }
}
