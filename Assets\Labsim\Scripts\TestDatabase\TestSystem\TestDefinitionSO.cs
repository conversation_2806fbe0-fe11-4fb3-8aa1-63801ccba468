
// using UnityEngine;
// using System.Collections.Generic;

// namespace Labsim
// {
//     [CreateAssetMenu(fileName = "NewTestDefinition", menuName = "Lab/Test Definition", order = 0)]
//     public class TestDefinitionSO : ScriptableObject
//     {
//         [Tooltip("Bu test tanımı için programatik, benzersiz bir kimlik. Örn: HEMOGRAM")]
//         public string testNameID;

//         [Tooltip("Kullanıcı arayüzünde gösterilecek test adı. Örn: Hemogram")]
//         public string displayName;

//         public TestCategory category;

//         [Tooltip("Bu testin rengi. Terminal ekranında gösterilecek.")]
//         public TestColor testColor = TestColor.White;

//         [Tooltip("Bu testin sahip olduğu parametrelerin listesi.")]
//         public List<ParameterDefinition> parameters = new List<ParameterDefinition>();
//     }
// }