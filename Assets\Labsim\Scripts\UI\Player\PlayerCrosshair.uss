/* Crosshair Container - Full Screen */
.crosshair-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    width: 100%;
    height: 100%;
}

/* Crosshair Dot - White Center Dot */
.crosshair-dot {
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
    opacity: 1;
    border: 2px solid black;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
}