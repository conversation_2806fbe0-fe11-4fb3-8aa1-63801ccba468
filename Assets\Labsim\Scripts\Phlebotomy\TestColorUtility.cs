// using UnityEngine;

// public static class TestColorUtility
// {
//     public static Color GetColorFromTestColor(Labsim.TestColor testColor)
//     {
//         switch (testColor)
//         {
//             case Labsim.TestColor.Red:
//                 return Color.red;
//             case Labsim.TestColor.Blue:
//                 return Color.blue;
//             case Labsim.TestColor.Green:
//                 return Color.green;
//             case Labsim.TestColor.Yellow:
//                 return Color.yellow;
//             case Labsim.TestColor.Orange:
//                 return new Color(1f, 0.5f, 0f); // Orange
//             case Labsim.TestColor.Purple:
//                 return new Color(0.5f, 0f, 1f); // Purple
//             case Labsim.TestColor.Pink:
//                 return new Color(1f, 0.75f, 0.8f); // Pink
//             case Labsim.TestColor.Cyan:
//                 return Color.cyan;
//             case Labsim.TestColor.Gray:
//                 return Color.gray;
//             case Labsim.TestColor.White:
//                 return Color.white;
//             default:
//                 return Color.white;
//         }
//     }
// }