using System;
using System.Collections.Generic;
using UnityEngine;

namespace LabTestSystem
{
    // Test durumu enum
    public enum TestStatus
    {
        Requested = 0,
        InProgress = 1,
        Completed = 2,
        Cancelled = 3,
        Failed = 4
    }

    // Test sonuç değeri
    [System.Serializable]
    public class TestResult
    {
        public string parameterName;
        public string value;
        public bool isAbnormal; // Normal aralık dışında mı?
        
        public TestResult(string name, string val)
        {
            parameterName = name;
            value = val;
            isAbnormal = false;
        }
    }

    // Tek bir test örneği (hasta için oluşturulan test)
    [System.Serializable]
    public class TestInstance
    {
        public int instanceTestId; // Benzersiz test örnek ID'si
        public string testDefinitionId; // Hangi test tanımına ait (TestDefinitionSO'nun ID'si)
        public string patientId;
        public TestStatus status = TestStatus.Requested;
        public DateTime dateRequested;
        public DateTime? dateCompleted;
        
        // Test sonuçları
        public Dictionary<string, TestResult> parameterResults;
        
        // Ek bilgiler
        public string requestedBy; // İsteyen doktor
        public string performedBy; // Testi yapan teknisyen
        public string notes;
        
        public TestInstance(int id, string testDefId, string patId)
        {
            instanceTestId = id;
            testDefinitionId = testDefId;
            patientId = patId;
            dateRequested = DateTime.Now;
            parameterResults = new Dictionary<string, TestResult>();
        }
        
        // Parametre sonucu ekle/güncelle
        public void SetParameterResult(string parameterName, string value)
        {
            if (parameterResults.ContainsKey(parameterName))
            {
                parameterResults[parameterName].value = value;
            }
            else
            {
                parameterResults.Add(parameterName, new TestResult(parameterName, value));
            }
        }
        
        // Test sonuçlarını al
        public TestResult GetParameterResult(string parameterName)
        {
            return parameterResults.ContainsKey(parameterName) ? 
                   parameterResults[parameterName] : null;
        }
        
        // Testi tamamla
        public void CompleteTest()
        {
            status = TestStatus.Completed;
            dateCompleted = DateTime.Now;
        }
        
        // Test tamamlandı mı?
        public bool IsCompleted()
        {
            return status == TestStatus.Completed;
        }
    }

    // Hasta test koleksiyonu
    [System.Serializable]
    public class PatientTests
    {
        public string patientId;
        public List<TestInstance> tests;
        
        public PatientTests(string id)
        {
            patientId = id;
            tests = new List<TestInstance>();
        }
        
        // Test ekle
        public void AddTest(TestInstance test)
        {
            if (test != null && test.patientId == patientId)
            {
                tests.Add(test);
            }
        }
        
        // Test sil
        public bool RemoveTest(int instanceTestId)
        {
            return tests.RemoveAll(t => t.instanceTestId == instanceTestId) > 0;
        }
        
        // Test bul
        public TestInstance GetTest(int instanceTestId)
        {
            return tests.Find(t => t.instanceTestId == instanceTestId);
        }
        
        // Belirli türdeki testleri getir
        public List<TestInstance> GetTestsByDefinition(string testDefinitionId)
        {
            return tests.FindAll(t => t.testDefinitionId == testDefinitionId);
        }
        
        // Duruma göre testleri getir
        public List<TestInstance> GetTestsByStatus(TestStatus status)
        {
            return tests.FindAll(t => t.status == status);
        }
    }
}