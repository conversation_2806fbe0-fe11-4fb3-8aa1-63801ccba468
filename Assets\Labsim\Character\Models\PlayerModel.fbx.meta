fileFormatVersion: 2
guid: c735ec8df00c3434484c55b2d0431f57
ModelImporter:
  serializedVersion: 24200
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1001_Diffuse
    second: {fileID: 2800000, guid: 899d2521674ec0445aa89dfc523f09e1, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1001_Glossiness
    second: {fileID: 2800000, guid: 2a23da6c377369c48957f7515a461326, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1001_Normal
    second: {fileID: 2800000, guid: de28b531c8f742142b8232a1b4697b31, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1001_Specular
    second: {fileID: 2800000, guid: 9ce022b7ea30a054eaad4f9fa5dd2083, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1002_Diffuse
    second: {fileID: 2800000, guid: d00315fa15923c34581dc529adff5b70, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1002_Glossiness
    second: {fileID: 2800000, guid: 9b5892b60ae5c944d9ae38b045e61595, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1002_Normal
    second: {fileID: 2800000, guid: 16492b7ebe692544a87b43bf7bed2106, type: 3}
  - first:
      type: UnityEngine:Texture2D
      assembly: UnityEngine.CoreModule
      name: Ch16_1002_Specular
    second: {fileID: 2800000, guid: 8fa23f615f762c34fa5e28fe96adba3a, type: 3}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    generateMeshLods: 0
    meshLodGenerationFlags: 0
    maximumMeshLod: -1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
