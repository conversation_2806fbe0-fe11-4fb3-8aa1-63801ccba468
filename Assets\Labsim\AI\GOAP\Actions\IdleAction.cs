// using CrashKonijn.Agent.Core;
// using CrashKonijn.Goap.Runtime;
// using CrashKonijn.Agent.Runtime;
// using UnityEngine;
// using Random = UnityEngine.Random;

// namespace Labsim.AI.GOAP.Actions
// {
//     // The GoapId attribute is used to identify the action, even when you change the name
//     // This is used when using the Scriptable Object method of configuring actions
//     [GoapId("Idle-ccc6f46c-1626-44aa-b90d-1b2741642166")]
//     public class IdleAction : GoapActionBase<IdleAction.Data>
//     {
      
//         public override void Start(IMonoAgent agent, Data data)
//         {
            
//         }

//         public override IActionRunState Perform(IMonoAgent agent, Data data, IActionContext context)
//         {
            
//             if (data.patient != null && data.patient.isCalled)
//             {
                
//                 Debug.Log("IdleAction interrupted - patient is called");
//                 return ActionRunState.Completed;
//             }
            
//             return ActionRunState.Continue;
//         }

//         public override void Complete(IMonoAgent agent, Data data)
//         {
//             Debug.Log("IdleAction Complete");
//         }

      
        
       
//         public class Data : IActionData
//         {
//             public ITarget Target { get; set; }
//             public float Timer { get; set; }
            
//             [GetComponent]
//             public Patient patient { get; set; }
//         }
//     }
// }