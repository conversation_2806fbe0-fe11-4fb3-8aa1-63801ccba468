{"version": 1, "name": "PlayerInput", "maps": [{"name": "Player", "id": "f62a4b92-ef5e-4175-8f4c-c9075429d32c", "actions": [{"name": "Move", "type": "Value", "id": "6bc1aaf4-b110-4ff7-891e-5b9fe6f32c4d", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "2690c379-f54d-45be-a724-414123833eb4", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "8c4abdf8-4099-493a-aa1a-129acec7c3df", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "82180e2c-842e-4a39-84b4-740869d56e4e", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Sprint", "type": "<PERSON><PERSON>", "id": "85032bb1-4f1d-4a47-b749-7560088baa77", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "WASD", "id": "b7594ddb-26c9-4ba2-bd5a-901468929edc", "path": "2DVector(mode=1)", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "2063a8b5-6a45-43de-851b-65f3d46e7b58", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "64e4d037-32e1-4fb9-80e4-fc7330404dfe", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "0fce8b11-5eab-4e4e-a741-b732e7b20873", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "7bdda0d6-57a8-47c8-8238-8aecf3110e47", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "bb94b405-58d3-4998-8535-d705c1218a98", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "929d9071-7dd0-4368-9743-6793bb98087e", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "28abadba-06ff-4d37-bb70-af2f1e35a3b9", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "45f115b6-9b4f-4ba8-b500-b94c93bf7d7e", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "e2f9aa65-db06-4c5b-a2e9-41bc8acb9517", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "StickDeadzone", "groups": "Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ed66cbff-2900-4a62-8896-696503cfcd31", "path": "<Pointer>/delta", "interactions": "", "processors": "InvertVector2(invertX=false),ScaleVector2(x=0.05,y=0.05)", "groups": "KeyboardMouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d1d171b6-19d8-47a6-ba3a-71b6a8e7b3c0", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "InvertVector2(invertX=false),StickDeadzone,ScaleVector2(x=300,y=300)", "groups": "Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1bd55a0b-761e-4ae4-89ae-8ec127e08a29", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9f973413-5e27-4239-acee-38c4a63feeba", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b0e69fe9-84bc-421b-b2e5-d67268434c13", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "KeyboardMouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c6ab2d06-67e7-4cd8-b3af-39fc4b5988d8", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}]}, {"name": "Terminal", "id": "8813dc4e-81c5-4bce-9395-49daefc3f4c6", "actions": [{"name": "TerminalLook", "type": "Value", "id": "dac2c1ab-9227-4711-b509-e691fde3b477", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "TerminalExit", "type": "<PERSON><PERSON>", "id": "101119a0-7df5-47eb-8ef1-5f65860e6118", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TerminalInteract", "type": "<PERSON><PERSON>", "id": "3669078f-8fa8-4482-a94e-68c0e460fc00", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "PointerPosition", "type": "Value", "id": "956c1fef-260b-484e-b628-61b5e1f3d378", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "<PERSON><PERSON>", "id": "317653a6-d7ee-419f-8b56-b1b051afe5f0", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "6ba98769-0958-4093-9cff-a7de4f5149d6", "path": "<Mouse>/delta", "interactions": "", "processors": "InvertVector2(invertX=false),ScaleVector2(x=0.05,y=0.05)", "groups": "", "action": "TerminalLook", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d6611317-be1e-4280-94a9-3bb185f4a573", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "", "action": "TerminalExit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c62db964-42dc-44e1-a4cf-fcd7c1afd204", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "", "action": "TerminalInteract", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "33a24987-4b18-4bb5-9bdd-d2bd3bdbc344", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "PointerPosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e08061c7-c1ad-4cec-9ec7-2981d47f8ab7", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "", "action": "Click", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "KeyboardMouse", "bindingGroup": "KeyboardMouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": true, "isOR": false}, {"devicePath": "<XInputController>", "isOptional": true, "isOR": false}, {"devicePath": "<DualShockGamepad>", "isOptional": true, "isOR": false}]}, {"name": "Xbox Controller", "bindingGroup": "Xbox Controller", "devices": []}, {"name": "PS4 Controller", "bindingGroup": "PS4 Controller", "devices": []}]}