// PatientNameData.cs (Yeni bir C# dosyası oluştur)
using System;
using UnityEngine;
[Serializable] // JsonUtility veya Newtonsoft.Json tarafından kullanılabilmesi için
public class PatientNameData
{
    // JSON dosyasındaki alan adlar<PERSON> (case sensitive olabilir!)
    public string firstName;
    public string lastName;
    public string gender; // JSON'dan string olarak okuyup sonra enum'a çevireceğiz

    // Helper metot: JSON'daki string'i Gender enum'una çevirir
    public Gender GetGenderEnum()
    {
        if (Enum.TryParse<Gender>(gender, true, out Gender result)) // 'true' -> case insensitive
        {
            return result;
        }
        else
        {
            Debug.LogWarning($"Could not parse gender string '{gender}'. Defaulting to Gender.Male.");
            return Gender.Male; // Veya bir hata fırlatılabilir
        }
    }
}