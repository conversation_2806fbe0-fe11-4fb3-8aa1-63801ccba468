// using CrashKonijn.Goap.Core;
// using CrashKonijn.Goap.Runtime;
// using Labsim.AI.GOAP.Actions;
// using Labsim.AI.GOAP.Goals;
// using Labsim.AI.GOAP.Sensors;

// namespace Labsim.AI.GOAP.Capabilities
// {
//     public class PearCapability : CapabilityFactoryBase
//     {
//         public override ICapabilityConfig Create()
//         {
//             var builder = new CapabilityBuilder("PearCapability");

//             builder.AddGoal<PickupPearGoal>()
//                 .AddCondition<PearCount>(Comparison.GreaterThanOrEqual, 3);
            
//             builder.AddAction<PickupPearAction>()
//                 .AddEffect<PearCount>(EffectType.Increase)
//                 .SetTarget<ClosestPear>();

//             builder.AddMultiSensor<PearSensor>();

//             return builder.Build();
//         }
//     }
// }