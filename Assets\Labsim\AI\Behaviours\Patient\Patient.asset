%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-2030189991599672737
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5c02bb70996b49eba31d0c206e28da24, type: 3}
  m_Name: Patient Blackboard
  m_EditorClassIdentifier: Unity.Behavior::Unity.Behavior.RuntimeBlackboardAsset
  VersionTimestamp: 638923671146925022
  AssetID:
    m_Value0: 10707464584450882693
    m_Value1: 7097671187925944041
  m_Blackboard:
    m_Variables:
    - rid: 6387143345795236099
    - rid: 6387143352016437357
  m_SharedBlackboardVariableGuids: []
  references:
    version: 2
    RefIds:
    - rid: 6387143345795236099
      type: {class: 'BlackboardVariable`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 1
          m_Value1: 0
        Name: Self
        m_Value: {fileID: 0}
    - rid: 6387143352016437357
      type: {class: 'BlackboardVariable`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 10434519137092313975
          m_Value1: 3288809942414530722
        Name: TargetTerminal
        m_Value: {fileID: 0}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bad8f2220607dac4db5082ff333fafb8, type: 3}
  m_Name: Patient
  m_EditorClassIdentifier: Unity.Behavior.Authoring::Unity.Behavior.BehaviorAuthoringGraph
  Blackboard: {fileID: 2493862508650474371}
  m_Description: 
  m_Nodes:
  - rid: 6387143345795236097
  - rid: 6387143345795236116
  - rid: 6387143350596927700
  - rid: 6387143362474148080
  m_VersionTimestamp: 638930350662221009
  m_DebugInfo: {fileID: 3149683611554270868}
  m_RuntimeGraph: {fileID: 647440140765001889}
  AssetID:
    m_Value0: 10707464584450882693
    m_Value1: 7097671187925944041
  Story:
    Story: 
    StoryVariableNames: []
    Variables: []
  m_NodeModelsInfo:
  - Name: On Start
    Story: 
    RuntimeTypeID:
      m_Value0: 3335272451348827663
      m_Value1: 11549843281177505721
    Variables: []
    NamedChildren: []
  - Name: Navigate To Target
    Story: '[Agent] navigates to [Target]'
    RuntimeTypeID:
      m_Value0: 14505029119854362939
      m_Value1: 1167385928027178409
    Variables:
    - Name: Agent
      Type:
        m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    - Name: Target
      Type:
        m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    - Name: Speed
      Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      Tooltip: 
    - Name: DistanceThreshold
      Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      Tooltip: 
    - Name: AnimatorSpeedParam
      Type:
        m_SerializableType: System.String, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      Tooltip: 
    - Name: SlowDownDistance
      Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      Tooltip: 
    - Name: m_TargetPositionMode
      Type:
        m_SerializableType: Unity.Behavior.NavigateToTargetAction+TargetPositionMode,
          Unity.Behavior, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      Tooltip: 
    NamedChildren: []
  - Name: DetectTerminal
    Story: Detect Terminal
    RuntimeTypeID:
      m_Value0: 12439202453253864587
      m_Value1: 4635235276500879689
    Variables:
    - Name: Agent
      Type:
        m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    - Name: ActionTerminalTarget
      Type:
        m_SerializableType: UnityEngine.Transform, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    - Name: IsTerminalCalled
      Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      Tooltip: 
    NamedChildren: []
  - Name: PhlebotomyTerminalIdle
    Story: Patient waits idle near by phlebotomy terminal.
    RuntimeTypeID:
      m_Value0: 5747805430722225452
      m_Value1: 2614949274764926265
    Variables:
    - Name: Agent
      Type:
        m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    NamedChildren: []
  - Name: PatientRigAction
    Story: Patient Rig Action
    RuntimeTypeID:
      m_Value0: 14162903840781909635
      m_Value1: 14173295033417136289
    Variables: []
    NamedChildren: []
  - Name: PatientRig
    Story: asdasd
    RuntimeTypeID:
      m_Value0: 17124050624535076523
      m_Value1: 2175178419787537459
    Variables: []
    NamedChildren: []
  - Name: Set Variable Value
    Story: 'Set [Variable] value to [Value]'
    RuntimeTypeID:
      m_Value0: 5494721648866555964
      m_Value1: 18024545711220129068
    Variables:
    - Name: Variable
      Type:
        m_SerializableType: Unity.Behavior.BlackboardVariable, Unity.Behavior, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    - Name: Value
      Type:
        m_SerializableType: Unity.Behavior.BlackboardVariable, Unity.Behavior, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    NamedChildren: []
  - Name: Wait for Event Message
    Story: 
    RuntimeTypeID:
      m_Value0: 15586893896841764598
      m_Value1: 11910781537861691316
    Variables:
    - Name: ChannelVariable
      Type:
        m_SerializableType: Unity.Behavior.BlackboardVariable, Unity.Behavior, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    NamedChildren: []
  - Name: NavigateToTerminal
    Story: Patient Navigate Terminal
    RuntimeTypeID:
      m_Value0: 1851641524182001091
      m_Value1: 1008726909888144004
    Variables: []
    NamedChildren: []
  - Name: Start On Event Message
    Story: 'When a message is received on [ChannelVariable]'
    RuntimeTypeID:
      m_Value0: 3356300611230043817
      m_Value1: 5497005381991559097
    Variables:
    - Name: ChannelVariable
      Type:
        m_SerializableType: Unity.Behavior.BlackboardVariable, Unity.Behavior, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      Tooltip: 
    NamedChildren: []
  m_Blackboards: []
  m_MainBlackboardAuthoringAsset: {fileID: 2493862508650474371}
  m_CommandBuffer:
    m_Commands: []
  m_SubgraphsInfo: []
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 6387143345795236097
      type: {class: StartNodeModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        Position: {x: -256.80002, y: -35.19997}
        ID:
          m_Value0: 10777433979745991574
          m_Value1: 11038413654768645973
        Parents: []
        PortModels:
        - rid: 6387143345795236098
        NodeType:
          m_SerializableType: Unity.Behavior.Start, Unity.Behavior, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        NodeTypeID:
          m_Value0: 3335272451348827663
          m_Value1: 11549843281177505721
        m_FieldValues: []
        Repeat: 1
        AllowMultipleRepeatsPerTick: 0
    - rid: 6387143345795236098
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: OutputPort
        m_PortDataFlowType: 1
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143345795236097
        m_Connections:
        - rid: 6387143350596927701
    - rid: 6387143345795236116
      type: {class: ActionNodeModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        Position: {x: -535.0892, y: 221.95992}
        ID:
          m_Value0: 12623146697111187150
          m_Value1: 610059161408567077
        Parents: []
        PortModels:
        - rid: 6387143345795236117
        - rid: 6387143345795236118
        NodeType:
          m_SerializableType: Unity.Behavior.NavigateToTargetAction, Unity.Behavior,
            Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        NodeTypeID:
          m_Value0: 14505029119854362939
          m_Value1: 1167385928027178409
        m_FieldValues:
        - rid: 6387143345795236119
        - rid: 6387143345795236120
        - rid: 6387143345795236121
        - rid: 6387143345795236122
        - rid: 6387143345795236123
        - rid: 6387143345795236124
        - rid: 6387143345795236125
    - rid: 6387143345795236117
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: InputPort
        m_PortDataFlowType: 0
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143345795236116
        m_Connections:
        - rid: 6387143350596927702
    - rid: 6387143345795236118
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: OutputPort
        m_PortDataFlowType: 1
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143345795236116
        m_Connections:
        - rid: 6387143362474148081
    - rid: 6387143345795236119
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: Agent
        Type:
          m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        LocalValue:
          rid: 6387143345795236126
        LinkedVariable:
          rid: 6387143345795236187
    - rid: 6387143345795236120
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: Target
        Type:
          m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        LocalValue:
          rid: 6387143345795236127
        LinkedVariable:
          rid: 6387143352016437356
    - rid: 6387143345795236121
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: Speed
        Type:
          m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        LocalValue:
          rid: 6387143345795236128
        LinkedVariable:
          rid: -2
    - rid: 6387143345795236122
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: DistanceThreshold
        Type:
          m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        LocalValue:
          rid: 6387143345795236129
        LinkedVariable:
          rid: -2
    - rid: 6387143345795236123
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: AnimatorSpeedParam
        Type:
          m_SerializableType: System.String, mscorlib, Version=*******, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        LocalValue:
          rid: 6387143345795236130
        LinkedVariable:
          rid: -2
    - rid: 6387143345795236124
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: SlowDownDistance
        Type:
          m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        LocalValue:
          rid: 6387143345795236131
        LinkedVariable:
          rid: -2
    - rid: 6387143345795236125
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: m_TargetPositionMode
        Type:
          m_SerializableType: Unity.Behavior.NavigateToTargetAction+TargetPositionMode,
            Unity.Behavior, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        LocalValue:
          rid: 6387143345795236132
        LinkedVariable:
          rid: -2
    - rid: 6387143345795236126
      type: {class: 'BlackboardVariable`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: {fileID: 0}
    - rid: 6387143345795236127
      type: {class: 'BlackboardVariable`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: {fileID: 0}
    - rid: 6387143345795236128
      type: {class: 'BlackboardVariable`1[[System.Single, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 5
    - rid: 6387143345795236129
      type: {class: 'BlackboardVariable`1[[System.Single, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 2
    - rid: 6387143345795236130
      type: {class: 'BlackboardVariable`1[[System.String, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 
    - rid: 6387143345795236131
      type: {class: 'BlackboardVariable`1[[System.Single, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 5
    - rid: 6387143345795236132
      type: {class: 'BlackboardVariable`1[[Unity.Behavior.NavigateToTargetAction/TargetPositionMode, Unity.Behavior]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 2
    - rid: 6387143345795236187
      type: {class: 'TypedVariableModel`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 1
          m_Value1: 0
        Name: Self
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
    - rid: 6387143350596927700
      type: {class: ActionNodeModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        Position: {x: -393.05008, y: 117.85008}
        ID:
          m_Value0: 5308612398489966365
          m_Value1: 12656614137311025854
        Parents: []
        PortModels:
        - rid: 6387143350596927701
        - rid: 6387143350596927702
        NodeType:
          m_SerializableType: DetectTerminalAction, Assembly-CSharp, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        NodeTypeID:
          m_Value0: 12439202453253864587
          m_Value1: 4635235276500879689
        m_FieldValues:
        - rid: 6387143350596927718
        - rid: 6387143352016437399
        - rid: 6387143350596927722
    - rid: 6387143350596927701
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: InputPort
        m_PortDataFlowType: 0
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143350596927700
        m_Connections:
        - rid: 6387143345795236098
    - rid: 6387143350596927702
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: OutputPort
        m_PortDataFlowType: 1
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143350596927700
        m_Connections:
        - rid: 6387143345795236117
    - rid: 6387143350596927718
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: Agent
        Type:
          m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        LocalValue:
          rid: 6387143350596927720
        LinkedVariable:
          rid: 6387143350596927776
    - rid: 6387143350596927720
      type: {class: 'BlackboardVariable`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: {fileID: 0}
    - rid: 6387143350596927722
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: IsTerminalCalled
        Type:
          m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        LocalValue:
          rid: 6387143350596927723
        LinkedVariable:
          rid: -2
    - rid: 6387143350596927723
      type: {class: 'BlackboardVariable`1[[System.Boolean, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 0
    - rid: 6387143350596927776
      type: {class: 'TypedVariableModel`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 1
          m_Value1: 0
        Name: Self
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
    - rid: 6387143352016437356
      type: {class: 'TypedVariableModel`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 10434519137092313975
          m_Value1: 3288809942414530722
        Name: TargetTerminal
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
    - rid: 6387143352016437399
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: ActionTerminalTarget
        Type:
          m_SerializableType: UnityEngine.Transform, UnityEngine.CoreModule, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        LocalValue:
          rid: 6387143352016437400
        LinkedVariable:
          rid: 6387143352016437401
    - rid: 6387143352016437400
      type: {class: 'BlackboardVariable`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: {fileID: 0}
    - rid: 6387143352016437401
      type: {class: 'TypedVariableModel`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 10434519137092313975
          m_Value1: 3288809942414530722
        Name: TargetTerminal
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
    - rid: 6387143362474148080
      type: {class: ActionNodeModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        Position: {x: -577.6459, y: 303.9799}
        ID:
          m_Value0: 9095975851904395873
          m_Value1: 16465288746799021862
        Parents: []
        PortModels:
        - rid: 6387143362474148081
        - rid: 6387143362474148082
        NodeType:
          m_SerializableType: PhlebotomyTerminalIdleAction, Assembly-CSharp, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        NodeTypeID:
          m_Value0: 5747805430722225452
          m_Value1: 2614949274764926265
        m_FieldValues:
        - rid: 6387143362474148101
    - rid: 6387143362474148081
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: InputPort
        m_PortDataFlowType: 0
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143362474148080
        m_Connections:
        - rid: 6387143345795236118
    - rid: 6387143362474148082
      type: {class: PortModel, ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        m_Name: OutputPort
        m_PortDataFlowType: 1
        m_IsFloating: 0
        m_NodeModel:
          rid: 6387143362474148080
        m_Connections: []
    - rid: 6387143362474148101
      type: {class: BehaviorGraphNodeModel/FieldModel, ns: Unity.Behavior, asm: Unity.Behavior.Authoring}
      data:
        FieldName: Agent
        Type:
          m_SerializableType: UnityEngine.GameObject, UnityEngine.CoreModule, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        LocalValue:
          rid: 6387143362474148102
        LinkedVariable:
          rid: 6387143362474148140
    - rid: 6387143362474148102
      type: {class: 'BlackboardVariable`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: {fileID: 0}
    - rid: 6387143362474148140
      type: {class: 'TypedVariableModel`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 1
          m_Value1: 0
        Name: Self
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
--- !u!114 &647440140765001889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: db920e62f70f420bb33c771449926fa4, type: 3}
  m_Name: Patient
  m_EditorClassIdentifier: Unity.Behavior::Unity.Behavior.BehaviorGraph
  Graphs:
  - rid: 6387143525377507580
  RootGraph:
    rid: 6387143525377507580
  m_DebugInfo: {fileID: 3149683611554270868}
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 6387143525377507571
      type: {class: 'BlackboardVariable`1[[System.Boolean, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 0
    - rid: 6387143525377507575
      type: {class: 'BlackboardVariable`1[[System.Single, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 5
    - rid: 6387143525377507576
      type: {class: 'BlackboardVariable`1[[System.Single, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 2
    - rid: 6387143525377507577
      type: {class: 'BlackboardVariable`1[[System.String, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 
    - rid: 6387143525377507578
      type: {class: 'BlackboardVariable`1[[System.Single, mscorlib]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 5
    - rid: 6387143525377507579
      type: {class: 'BlackboardVariable`1[[Unity.Behavior.NavigateToTargetAction/TargetPositionMode, Unity.Behavior]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: 2
    - rid: 6387143525377507580
      type: {class: BehaviorGraphModule, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        AuthoringAssetID:
          m_Value0: 10707464584450882693
          m_Value1: 7097671187925944041
        m_DebugInfo: {fileID: 0}
        BlackboardReference:
          rid: 6387143525377507581
        BlackboardGroupReferences: []
        Root:
          rid: 6387143525377507582
        m_ActiveNodes: []
        m_NodesToTick: []
        m_NodesToEnd:
          rid: 6387143525377507583
        m_EndedNodes:
          rid: 6387143525377507584
        m_VersionTimestamp: 638930350662221009
    - rid: 6387143525377507581
      type: {class: BlackboardReference, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        m_Blackboard:
          rid: 6387143525377507585
        m_Source: {fileID: -2030189991599672737}
    - rid: 6387143525377507582
      type: {class: Start, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        ID:
          m_Value0: 10777433979745991574
          m_Value1: 11038413654768645973
        Graph:
          rid: 6387143525377507580
        m_Parent:
          rid: -2
        m_Child:
          rid: 6387143525377507586
        Repeat: 1
        AllowMultipleRepeatsPerTick: 0
    - rid: 6387143525377507583
      type: {class: 'Stack`1[[Unity.Behavior.Node, Unity.Behavior]]', ns: System.Collections.Generic, asm: mscorlib}
      data: 
    - rid: 6387143525377507584
      type: {class: 'HashSet`1[[Unity.Behavior.Node, Unity.Behavior]]', ns: System.Collections.Generic, asm: System.Core}
      data: 
    - rid: 6387143525377507585
      type: {class: Blackboard, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        m_Variables:
        - rid: 6387143525377507587
        - rid: 6387143525377507588
    - rid: 6387143525377507586
      type: {class: SequenceComposite, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        ID:
          m_Value0: 5308612398489966365
          m_Value1: 12656614137311025854
        Graph:
          rid: 6387143525377507580
        m_Parent:
          rid: 6387143525377507582
        m_Children:
        - rid: 6387143525377507589
        - rid: 6387143525377507590
    - rid: 6387143525377507587
      type: {class: 'BlackboardVariable`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 1
          m_Value1: 0
        Name: Self
        m_Value: {fileID: 0}
    - rid: 6387143525377507588
      type: {class: 'BlackboardVariable`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 10434519137092313975
          m_Value1: 3288809942414530722
        Name: TargetTerminal
        m_Value: {fileID: 0}
    - rid: 6387143525377507589
      type: {class: DetectTerminalAction, ns: , asm: Assembly-CSharp}
      data:
        ID:
          m_Value0: 5308612398489966365
          m_Value1: 12656614137311025854
        Graph:
          rid: 6387143525377507580
        m_Parent:
          rid: 6387143525377507586
        Agent:
          rid: 6387143525377507587
        ActionTerminalTarget:
          rid: 6387143525377507588
        IsTerminalCalled:
          rid: 6387143525377507571
    - rid: 6387143525377507590
      type: {class: SequenceComposite, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        ID:
          m_Value0: 12623146697111187150
          m_Value1: 610059161408567077
        Graph:
          rid: 6387143525377507580
        m_Parent:
          rid: 6387143525377507586
        m_Children:
        - rid: 6387143525377507591
        - rid: 6387143525377507592
    - rid: 6387143525377507591
      type: {class: NavigateToTargetAction, ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        ID:
          m_Value0: 12623146697111187150
          m_Value1: 610059161408567077
        Graph:
          rid: 6387143525377507580
        m_Parent:
          rid: 6387143525377507590
        Agent:
          rid: 6387143525377507587
        Target:
          rid: 6387143525377507593
        Speed:
          rid: 6387143525377507575
        DistanceThreshold:
          rid: 6387143525377507576
        AnimatorSpeedParam:
          rid: 6387143525377507577
        SlowDownDistance:
          rid: 6387143525377507578
        m_TargetPositionMode:
          rid: 6387143525377507579
    - rid: 6387143525377507592
      type: {class: PhlebotomyTerminalIdleAction, ns: , asm: Assembly-CSharp}
      data:
        ID:
          m_Value0: 9095975851904395873
          m_Value1: 16465288746799021862
        Graph:
          rid: 6387143525377507580
        m_Parent:
          rid: 6387143525377507590
        Agent:
          rid: 6387143525377507587
    - rid: 6387143525377507593
      type: {class: 'ComponentToGameObjectBlackboardVariable`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior, asm: Unity.Behavior}
      data:
        GUID:
          m_Value0: 0
          m_Value1: 0
        Name: 
        m_Value: {fileID: 0}
        m_LinkedVariable:
          rid: 6387143525377507588
--- !u!114 &2493862508650474371
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dd922ae02c94c87a66e46a10a7319b9, type: 3}
  m_Name: Patient Blackboard
  m_EditorClassIdentifier: Unity.Behavior.Authoring::Unity.Behavior.BehaviorBlackboardAuthoringAsset
  AssetID:
    m_Value0: 10707464584450882693
    m_Value1: 7097671187925944041
  m_Variables:
  - rid: 6387143345795236107
  - rid: 6387143352016437355
  m_VersionTimestamp: 638923671146925022
  m_CommandBuffer:
    m_Commands: []
  m_RuntimeBlackboardAsset: {fileID: -2030189991599672737}
  references:
    version: 2
    RefIds:
    - rid: 6387143345795236107
      type: {class: 'TypedVariableModel`1[[UnityEngine.GameObject, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 1
          m_Value1: 0
        Name: Self
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
    - rid: 6387143352016437355
      type: {class: 'TypedVariableModel`1[[UnityEngine.Transform, UnityEngine.CoreModule]]', ns: Unity.Behavior.GraphFramework, asm: Unity.Behavior.GraphFramework}
      data:
        ID:
          m_Value0: 10434519137092313975
          m_Value1: 3288809942414530722
        Name: TargetTerminal
        IsExposed: 1
        m_IsShared: 0
        m_Value: {fileID: 0}
--- !u!114 &3149683611554270868
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b27bb6d9a2c8d540a10dff10acc543e, type: 3}
  m_Name: Patient Debug Info
  m_EditorClassIdentifier: Unity.Behavior::Unity.Behavior.BehaviorGraphDebugInfo
  m_CodeBreakPointsList: []
