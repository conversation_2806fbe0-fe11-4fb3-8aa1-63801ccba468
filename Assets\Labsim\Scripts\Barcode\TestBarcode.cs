using UnityEngine;
using UnityEngine.UIElements;
using Labsim;
using PurrNet;

public class TestBarcode : NetworkBehaviour , IDragable
{
    [Header("UI Components")]
    [SerializeField] private UIDocument m_uiDocument;

    [Header("Test Data")]
    [SerializeField] private SyncVar<int> testInstanceId = new(0, ownerAuth:true);
    [SerializeField] private string testNameID;

    private VisualElement m_rootElement;
    private Label m_testIdLabel;
    private Label m_testNameLabel;


    [SerializeField] private BoxCollider boxCollider;
    private Rigidbody rb;
    private GameObject originalPrefab;

    public string TerminalInteractionPrompt => "Test Barcode";

    // public override void OnSpawned(bool asServer)
    // {
    //     base.OnSpawned(asServer);
    //     InitializeUI();
    //     UpdateDisplay();
        
    //     boxCollider = GetComponent<BoxCollider>();
    //     rb = GetComponent<Rigidbody>();
        
    //     if (rb == null)
    //     {
    //         rb = gameObject.AddComponent<Rigidbody>();
    //     }
        
    //     rb.isKinematic = true;
    //     rb.useGravity = false;
        
    //     // Trigger setup for drop detection
    //     if (boxCollider != null)
    //     {
    //         boxCollider.isTrigger = false; // Normal collider for dragging
    //     }
       
    //     originalScale = transform.localScale;
    // }

   

    private void InitializeUI()
    {
        if (m_uiDocument == null)
        {
            Debug.LogError("[TestBarcode] UIDocument is not assigned!");
            return;
        }

        m_rootElement = m_uiDocument.rootVisualElement;

        // Find UI elements
        m_testIdLabel = m_rootElement.Q<Label>("TestIdLabel");
        m_testNameLabel = m_rootElement.Q<Label>("TestNameLabel");

        if (m_testIdLabel == null)
        {
            Debug.LogError("[TestBarcode] TestIdLabel not found in UXML!");
        }

        if (m_testNameLabel == null)
        {
            Debug.LogError("[TestBarcode] TestNameLabel not found in UXML!");
        }
    }

    private void UpdateDisplay()
    {
        if (m_testIdLabel != null)
        {
            //m_testIdLabel.text = $"ID: {testInstanceId.Value}";
        }

        if (m_testNameLabel != null)
        {
            string displayName = GetTestDisplayName();
            m_testNameLabel.text = displayName;
        }
    }

    private string GetTestDisplayName()
    {
        if (string.IsNullOrEmpty(testNameID))
        {
            return "Unknown Test";
        }

        // if (LabDatabaseManager.Instance != null)
        // {
        //     var testDefinition = LabDatabaseManager.Instance.GetTestDefinitionByID(testNameID);
        //     if (testDefinition != null)
        //     {
        //         return testDefinition.displayName;
        //     }
        // }

        return testNameID; // Fallback to ID if display name not found
    }


    // [ServerRpc]
    // public void SetTestData(int instanceId, string nameID)
    // {
    //     if (IsServer)
    //     {
    //         testInstanceId.Value = instanceId;
    //         testNameID = nameID;
            
    //         // Update clients with the new data
    //         UpdateTestDataClientRpc(instanceId, nameID);
    //     }
    // }
    
    [ObserversRpc]
    private void UpdateTestDataClientRpc(int instanceId, string nameID)
    {
        testNameID = nameID;
        if (m_rootElement != null)
        {
            UpdateDisplay();
        }
    }

    // public void SetTestData(TestResultInstance testInstance)
    // {
    //     if (testInstance != null)
    //     {
    //         //SetTestData(testInstance.instanceTestId, testInstance.testNameID_FK);
    //     }
    // }

    // public void TerminalInteract(NetworkIdentity interactorObject)
    // {
    //     if (interactorObject.TryGetComponent<PhlebotomyTerminal>(out var phlebotomyTerminal))
    //     {
            
    //     }
    // }

    private bool isDragging = false;
    private Vector3 originalScale;
    
    public bool IsOverValidDropZone { get; set; }
    public IDropable CurrentDropZone { get; set; }

    public void OnDragStart(Vector3 position)
    {
        isDragging = true;

        if (rb != null)
        {
            rb.isKinematic = true;
        }
        
       
        transform.localScale = originalScale * 0.1f;
    }

    public void OnDrag(Vector3 position)
    {
        if (isDragging && rb != null)
        {
            
            rb.MovePosition(position);
        }
    }

    public void OnDragEnd(Vector3 position)
    {
        isDragging = false;
        // Restore original scale
        transform.localScale = originalScale;
    }

    public Rigidbody GetRigidbody()
    {
        return rb;
    }
    
    void OnTriggerEnter(Collider other)
    {
        IDropable dropZone = other.GetComponent<IDropable>();
        if (dropZone != null)
        {
            IsOverValidDropZone = true;
            CurrentDropZone = dropZone;
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        IDropable dropZone = other.GetComponent<IDropable>();
        if (dropZone != null && dropZone == CurrentDropZone)
        {
            IsOverValidDropZone = false;
            CurrentDropZone = null;
        }
    }
    
    public void SetOriginalPrefab(GameObject prefab)
    {
        originalPrefab = prefab;
    }
    
    // public int GetTestInstanceId()
    // {
    //     return testInstanceId.Value;
    // }
    
    public void ReturnToPool()
    {
        // Restore original scale before returning to pool
        transform.localScale = originalScale;
        
        // if (NetworkIdentity != null && NetworkIdentity.IsSpawned)
        // {
        //     NetworkIdentity.Despawn();
        // }
    }
}
