using CrashKonijn.Agent.Core;
using CrashKonijn.Goap.Runtime;
using UnityEngine;
using UnityEngine.AI;

namespace Labsim.AI.GOAP
{
    public class IdleTargetSensor : LocalTargetSensorBase
    {
        [SerializeField] private float m_RandomRadius = 3f;
        
        public override void Created()
        {
        }
        
        public override void Update()
        {
        }

        public override ITarget Sense(IActionReceiver agent, IComponentReference references, ITarget existingTarget)
        {
            if (agent == null || agent.Transform == null || !agent.Transform.gameObject.activeInHierarchy)
            {
                return existingTarget;
            }
            
            // NetworkObject networkObject = agent.Transform.GetComponent<NetworkObject>();
            // if (networkObject != null && !networkObject.IsSpawned)
            // {
            //     return existingTarget;
            // }
            
            Vector3 randomPosition = GetRandomPosition(agent.Transform.position);
            
            // if (agent.Transform.gameObject.activeInHierarchy && (networkObject == null || networkObject.IsSpawned))
            // {
                
            // }
            
            return new PositionTarget(randomPosition);
        }

        private Vector3 GetRandomPosition(Vector3 agentPosition)
        {
            Vector2 randomCircle = Random.insideUnitCircle * m_RandomRadius;
            Vector3 randomPosition = agentPosition + new Vector3(randomCircle.x, 0f, randomCircle.y);
            
            if (NavMesh.SamplePosition(randomPosition, out NavMeshHit hit, m_RandomRadius, NavMesh.AllAreas))
            {
                return hit.position;
            }
            
            return agentPosition;
        }
    }
}