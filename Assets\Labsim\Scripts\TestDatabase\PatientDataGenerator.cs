using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using PurrNet; 
using Newtonsoft.Json;


public class PatientDataGenerator : MonoBehaviour
{

    [Header("Configuration")]
    [SerializeField]
    [Tooltip("Name of the JSON file (without extension) inside any 'Resources' folder containing patient names.")]
    private string patientNamesJsonResourceFile = "patient_names";

    [Header("Generation Parameters")]
    [Tooltip("Hastanın olabileceği minimum yaş.")]
    [Range(1, 120)] public int minAge = 25;

    [Tooltip("Hastanın olabileceği maksimum yaş.")]
    [Range(1, 120)] public int maxAge = 75;

    [Tooltip("Bir hastaya atanacak minimum test sayısı.")]
    [Range(0, 20)] public int minTestsToAssign = 1;

    [Tooltip("Bir hastaya atanacak maksimum test sayısı.")]
    [Range(0, 20)] public int maxTestsToAssign = 3;

    // Loaded name data from JSON
    private List<PatientNameData> _loadedNames = new List<PatientNameData>();
    // Random number generator
    private System.Random _rng = new System.Random();
    // Pre-grouped lists by gender for faster lookups
    private Dictionary<Gender, List<PatientNameData>> _namesByGender = new Dictionary<Gender, List<PatientNameData>>();
    // Track used full names to avoid duplicates
    private HashSet<string> _usedNames = new HashSet<string>();

    void Awake()
    {
    }

    private void OnValidate()
    {
        if (maxAge < minAge) maxAge = minAge;
        if (maxTestsToAssign < minTestsToAssign) maxTestsToAssign = minTestsToAssign;
        if (minTestsToAssign < 0) minTestsToAssign = 0;
        if (minAge < 1) minAge = 1;
        if (maxAge < 1) maxAge = 1;
    }

    public void InitializeOnServerStart()
    {
        // Ensure this logic only runs on the server.
        // if (!IsServer())
        // {
        //     Debug.LogWarning($"[{nameof(PatientDataGenerator)}] InitializeOnServerStart called on a non-server. Disabling component.");
        //     this.enabled = false; // Disable on clients if accidentally enabled
        //     return;
        // }

        
        LoadAndProcessNamesFromJson();
        _usedNames.Clear(); // Reset used names on initialization
    }

    private void LoadAndProcessNamesFromJson()
    {
        _loadedNames.Clear();
        _namesByGender.Clear();

        TextAsset jsonTextAsset = Resources.Load<TextAsset>(patientNamesJsonResourceFile);

        if (jsonTextAsset == null)
        {
            Debug.LogError($"[{nameof(PatientDataGenerator)}] JSON file FAILED TO LOAD at 'Resources/{patientNamesJsonResourceFile}'. " +
                           "Check file existence, spelling, and ensure it's in a 'Resources' folder.");
            return; // Stop processing if file not found
        }

        try
        {
            _loadedNames = JsonConvert.DeserializeObject<List<PatientNameData>>(jsonTextAsset.text);

            if (_loadedNames == null)
            {
                 Debug.LogError($"[{nameof(PatientDataGenerator)}] JSON Deserialization FAILED (resulted in null). Check JSON structure in '{patientNamesJsonResourceFile}'.");
                 _loadedNames = new List<PatientNameData>(); // Ensure list is not null on error
                 return; // Stop processing if deserialization failed
            }

            // Pre-group names by gender for performance
            // Initialize dictionary keys first
             foreach (Gender genderEnum in System.Enum.GetValues(typeof(Gender)))
            {
                _namesByGender[genderEnum] = new List<PatientNameData>();
            }
            // Populate the lists
            foreach(var nameData in _loadedNames)
            {
                Gender g = nameData.GetGenderEnum();
                if(_namesByGender.ContainsKey(g)) // Should always be true now
                {
                    _namesByGender[g].Add(nameData);
                }
            }


            
            foreach(var kvp in _namesByGender)
            {
                 
            }
        }
        catch (JsonException jsonEx)
        {
            Debug.LogError($"[{nameof(PatientDataGenerator)}] Error PARSING JSON file '{patientNamesJsonResourceFile}'. Invalid JSON format? Error: {jsonEx.Message}", this);
            _loadedNames = new List<PatientNameData>(); // Reset list on error
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[{nameof(PatientDataGenerator)}] An unexpected error occurred during JSON loading/processing '{patientNamesJsonResourceFile}'. Error: {ex.Message}", this);
            _loadedNames = new List<PatientNameData>(); // Reset list on error
        }
    }

    public bool GetRandomData(out string outFullName, out Gender outGender, out int outAge)
    {
        outFullName = "Default Patient";
        outGender = Gender.Male;
        outAge = minAge;

        // if (!IsServer())
        // {
        //     Debug.LogError($"[{nameof(PatientDataGenerator)}] {nameof(GetRandomData)} called on a non-server instance!", this);
        //     return false;
        // }

        // Randomly select a gender
        Gender[] genders = { Gender.Male, Gender.Female };
        outGender = genders[_rng.Next(genders.Length)];

        // Get random name for the selected gender
        string firstName, lastName;
        if (GetRandomNameForGender(outGender, out firstName, out lastName))
        {
            outFullName = $"{firstName} {lastName}";
            // Generate random age within configured limits
            outAge = _rng.Next(minAge, maxAge + 1);
            return true;
        }

        return false;
    }

    
    public bool GetRandomNameForGender(Gender targetGender, out string outFirstName, out string outLastName)
    {
        outFirstName = "DefaultFirst"; // Default values
        outLastName = "DefaultLast";

        // if (!IsServer())
        // {
        //      Debug.LogError($"[{nameof(PatientDataGenerator)}] {nameof(GetRandomNameForGender)} called on a non-server instance!", this);
        //      return false;
        // }

        // Try to get the pre-filtered list for the target gender
        if (_namesByGender.TryGetValue(targetGender, out List<PatientNameData> suitableNames) && suitableNames.Count > 0)
        {
            // Check if we've used all possible names for this gender
            if (_usedNames.Count >= suitableNames.Count)
            {
                Debug.LogWarning($"[{nameof(PatientDataGenerator)}] All names for gender '{targetGender}' have been used. Clearing used names history to allow reuse.");
                _usedNames.Clear();
            }

            // Try to find an unused name (with max attempts to prevent infinite loop)
            int maxAttempts = suitableNames.Count * 2; // Double the count to ensure we have enough attempts
            int attempts = 0;
            bool foundUnusedName = false;
            PatientNameData selected = null;

            while (!foundUnusedName && attempts < maxAttempts)
            {
                selected = suitableNames[_rng.Next(suitableNames.Count)];
                string fullName = $"{selected.firstName} {selected.lastName}";

                if (!_usedNames.Contains(fullName))
                {
                    _usedNames.Add(fullName);
                    foundUnusedName = true;
                    break;
                }
                attempts++;
            }

            // If we couldn't find unused name after all attempts, use the last selected one anyway
            if (!foundUnusedName && selected != null)
            {
                string fullName = $"{selected.firstName} {selected.lastName}";
                Debug.LogWarning($"[{nameof(PatientDataGenerator)}] Could not find unused name after {attempts} attempts. Using: {fullName}");
                if (!_usedNames.Contains(fullName))
                {
                    _usedNames.Add(fullName);
                }
            }

            if (selected != null)
            {
                outFirstName = selected.firstName;
                outLastName = selected.lastName;
                return true;
            }

            return false;
        }
        else
        {
            // Fallback: No names found for the specific gender, try picking from the global list
            Debug.LogWarning($"[{nameof(PatientDataGenerator)}] No names found specifically for gender '{targetGender}'. Attempting fallback to global list.");
            if (_loadedNames.Count > 0)
            {
                // Try to find an unused name from the global list
                int maxAttempts = _loadedNames.Count * 2;
                int attempts = 0;
                bool foundUnusedName = false;
                PatientNameData selected = null;

                while (!foundUnusedName && attempts < maxAttempts)
                {
                    selected = _loadedNames[_rng.Next(_loadedNames.Count)];
                    string fullName = $"{selected.firstName} {selected.lastName}";

                    if (!_usedNames.Contains(fullName))
                    {
                        _usedNames.Add(fullName);
                        foundUnusedName = true;
                        break;
                    }
                    attempts++;
                }

                if (selected != null)
                {
                    outFirstName = selected.firstName;
                    outLastName = selected.lastName;
                    return true;
                }
                return false;
            }
            else
            {
                // Critical Error: Initialization likely failed, no names available at all.
                 Debug.LogError($"[{nameof(PatientDataGenerator)}] Cannot assign name. Both gender-specific and global name lists are empty. Initialization might have failed.", this);
                 return false; // No names available
            }
        }
    }

   
    // private bool IsServer()
    // {
    //     return NetworkManager.Singleton != null && NetworkManager.Singleton.IsServer;
    // }
}