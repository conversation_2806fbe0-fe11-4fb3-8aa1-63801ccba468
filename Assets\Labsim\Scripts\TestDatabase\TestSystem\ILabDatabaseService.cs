// using System.Collections.Generic;

// namespace Labsim
// {
//     public interface ILabDatabaseService
//     {
//         void LoadDatabase();
//         void SaveDatabase();
//         PatientProfile GetOrCreatePatient(string patientId);
//         TestResultInstance AddTestToPatient(string patientId, string testDefinitionID);
//         List<TestResultInstance> GetTestsForPatient(string patientId);
//         TestResultInstance GetSpecificTestForPatient(string patientId, int instanceTestId);
//         bool UpdateTestParameterValue(string patientId, int instanceTestId, string parameterName, object value);
//         bool UpdateTestStatus(string patientId, int instanceTestId, TestStatus newStatus);
//         List<string> GetAvailableTestDefinitionIDs();
//         TestDefinitionSO GetTestDefinitionByID(string testDefinitionID_FK);
//     }
// }