using UnityEngine;
using UnityEngine.UIElements;
using PurrNet;

public class PlayerUICrosshairController : NetworkBehaviour
{
    [Head<PERSON>("UI References")]
    [SerializeField] private UIDocument m_CrosshairUIDocument;
    
    private VisualElement m_CrosshairContainer;
    private VisualElement m_CrosshairDot;
    private bool m_IsVisible = true;

    void Start()
    {
        // Only initialize UI for the local player (owner)
        if (isOwner)
        {
            InitializeUI();
        }
    }

    private void InitializeUI()
    {
        if (m_CrosshairUIDocument == null)
            m_CrosshairUIDocument = GetComponent<UIDocument>();

        if (m_CrosshairUIDocument == null)
        {
            Debug.LogError("PlayerUICrosshairController: UIDocument component not found!");
            return;
        }

        var root = m_CrosshairUIDocument.rootVisualElement;
        
        // Get UI elements
        m_CrosshairContainer = root.Q<VisualElement>("CrosshairContainer");
        m_CrosshairDot = root.Q<VisualElement>("CrosshairDot");

        if (m_CrosshairContainer == null || m_CrosshairDot == null)
        {
            Debug.LogError("PlayerUICrosshairController: Could not find crosshair elements!");
            return;
        }

        // Initialize as visible for owner only
        ShowCrosshair();
    }

    public void ShowCrosshair()
    {
        // Only allow local player to control their crosshair
        if (!isOwner) return;
        
        if (m_CrosshairContainer != null)
        {
            m_CrosshairContainer.style.display = DisplayStyle.Flex;
            m_IsVisible = true;
        }
    }

    public void HideCrosshair()
    {
        // Only allow local player to control their crosshair
        if (!isOwner) return;
        
        if (m_CrosshairContainer != null)
        {
            m_CrosshairContainer.style.display = DisplayStyle.None;
            m_IsVisible = false;
        }
    }

    public void ToggleCrosshair()
    {
        // Only allow local player to control their crosshair
        if (!isOwner) return;
        
        if (m_IsVisible)
            HideCrosshair();
        else
            ShowCrosshair();
    }
}
