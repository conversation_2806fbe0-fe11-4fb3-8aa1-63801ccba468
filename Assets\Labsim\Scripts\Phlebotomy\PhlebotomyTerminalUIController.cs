using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using Labsim;
using LabTestSystem;
using System;
using PurrNet;

public class PhlebotomyTerminalUIController : NetworkBehaviour, ITerminalComponent
{
    [Header("UI Components")]
    [SerializeField] private UIDocument m_uiDocument;

    //Broadcast events
    public event Action OnPrintRequested;

    #region UI Elements
    private VisualElement m_mainContainer;
    private Label m_patientIdLabel;
    private VisualElement m_testsList;
    private VisualElement m_noTestsMessage;
    private ScrollView m_testsScrollView;
    private Button m_printButton;

    public ITerminalServiceProvider ServiceProvider { get; set; }
    #endregion

    private string currentPatientId;

    private void Awake()
    {
        InitializeUI();
    }

    private void Start()
    {
        if (m_uiDocument != null)
        {
            m_uiDocument.rootVisualElement.style.display = DisplayStyle.Flex;
        }
        
    }


    private void InitializeUI()
    {
        if (m_uiDocument == null)
        {
            m_uiDocument = GetComponentInChildren<UIDocument>();

            if (m_uiDocument == null)
            {
                Debug.LogError("[PhlebotomyTerminal] UIDocument component not found!");
                return;
            }
        }

        var root = m_uiDocument.rootVisualElement;

        m_mainContainer = root.Q<VisualElement>("MainContainer");
        m_patientIdLabel = root.Q<Label>("PatientIdLabel");
        m_testsList = root.Q<VisualElement>("TestsList");
        m_noTestsMessage = root.Q<VisualElement>("NoTestsMessage");
        m_testsScrollView = root.Q<ScrollView>("TestsScrollView");
        m_printButton = root.Q<Button>("PrintButton");

        if (m_printButton != null)
        {
            m_printButton.clicked += OnPrintButtonClicked;
        }
        else
        {
            Debug.LogError("[PhlebotomyTerminalUIController] Print button not found in UXML!");
        }

        if (m_mainContainer == null)
        {
            Debug.LogError("[PhlebotomyTerminal] MainContainer not found in UXML!");
        }
    }

    protected override void OnSpawned(bool asServer)
    {
        base.OnSpawned(asServer);

        // Event'leri subscribe etmeden önce unsubscribe et (çoklu subscribe'ı önlemek için)
        var terminal = ServiceProvider.GetService<PhlebotomyTerminal>();
        var interactable = ServiceProvider.GetService<PhlebotomyTerminalInteractable>();
        var database = InstanceHandler.GetInstance<LabTestDatabase>();

        terminal.OnPatientScanned -= HandlePatientScanned;
        interactable.OnTerminalEntered -= HandleTerminalEntered;
        database.OnPatientTestsReceived -= VisualizePatientTests;

        // Şimdi subscribe et
        terminal.OnPatientScanned += HandlePatientScanned;
        interactable.OnTerminalEntered += HandleTerminalEntered;
        database.OnPatientTestsReceived += VisualizePatientTests;
    }



    private  void OnDestroy()
    {
        if (ServiceProvider != null)
        {
            var terminal = ServiceProvider.GetService<PhlebotomyTerminal>();
            if (terminal != null)
            {
                terminal.OnPatientScanned -= HandlePatientScanned;
            }
        }

        // Always unsubscribe from the event
        if (InstanceHandler.TryGetInstance<LabTestDatabase>(out var database))
        {
            database.OnPatientTestsReceived -= VisualizePatientTests;
        }

        if (m_printButton != null)
        {
            m_printButton.clicked -= OnPrintButtonClicked;
        }
    }

    private void HandleTerminalEntered(IInteractor interactor)
    {
        RequestOwnershipServerRpc(interactor.playerID);
    }

    [ServerRpc(requireOwnership:false)]
    void RequestOwnershipServerRpc(PlayerID playerID)
    {
        GiveOwnership(playerID);
    }

    private void HandlePatientScanned(int patientId)
    {
        if (!isOwner) return; // Sadece owner client işlem yapsın

        currentPatientId = patientId.ToString();
        InstanceHandler.GetInstance<LabTestDatabase>().RequestGetPatientTestsServerRpc(currentPatientId);
    }

   
    private void VisualizePatientTests(List<TestInstance> tests)
    {
        if (!isOwner) return; 
        if (m_testsList == null || m_testsScrollView == null)
        {
            Debug.LogError("[PhlebotomyTerminalUIController] UI elements not found!");
            return;
        }

        m_testsList.Clear();

        if (tests == null || tests.Count == 0)
        {
            ShowNoTestsMessage();
            return;
        }

        HideNoTestsMessage();
        Debug.Log($"[PhlebotomyTerminalUIController] Visualizing {tests.Count} tests");

        foreach (TestInstance test in tests)
        {
            CreateTestUIElement(test);
        }
    }

    private void CreateTestUIElement(TestInstance test)
    {
        VisualElement testElement = new VisualElement();
        testElement.AddToClassList("test-item");
        
        // Test tipine göre renk ayarla (örnek renkler)
        Color testColor = GetTestColor(test.testDefinitionId);
        testElement.style.backgroundColor = testColor;

        Label testNameLabel = new Label(test.testDefinitionId);
        testNameLabel.AddToClassList("test-name");
        testNameLabel.style.color = Color.white;
        testElement.Add(testNameLabel);

        Label testIdLabel = new Label($"ID: {test.instanceTestId}");
        testIdLabel.AddToClassList("test-id");
        testIdLabel.style.color = Color.white;
        testElement.Add(testIdLabel);

        Label statusLabel = new Label($"Status: {GetStatusText(test.status)}");
        statusLabel.AddToClassList("test-status");
        statusLabel.style.color = GetStatusColor(test.status);
        testElement.Add(statusLabel);

        m_testsList.Add(testElement);
    }

    private Color GetTestColor(string testDefinitionId)
    {
        // Test tipine göre renk döndür
        switch (testDefinitionId)
        {
            case "HEMOGRAM":
                return new Color(0.8f, 0.2f, 0.2f, 0.9f); // Kırmızı
            case "COAGULATION":
                return new Color(0.6f, 0.2f, 0.8f, 0.9f); // Mor
            case "URINALYSIS_COMPLETE":
                return new Color(0.8f, 0.8f, 0.2f, 0.9f); // Sarı
            case "BIOCHEMISTRY":
                return new Color(0.2f, 0.6f, 0.8f, 0.9f); // Mavi
            default:
                return new Color(0.5f, 0.5f, 0.5f, 0.9f); // Gri
        }
    }

    private string GetStatusText(TestStatus status)
    {
        switch (status)
        {
            case TestStatus.Requested: return "Requested";
            case TestStatus.InProgress: return "In Progress";
            case TestStatus.Completed: return "Completed";
            case TestStatus.Cancelled: return "Cancelled";
            case TestStatus.Failed: return "Failed";
            default: return "Unknown";
        }
    }

    private Color GetStatusColor(TestStatus status)
    {
        switch (status)
        {
            case TestStatus.Requested: return Color.yellow;
            case TestStatus.InProgress: return Color.cyan;
            case TestStatus.Completed: return Color.green;
            case TestStatus.Cancelled: return Color.gray;
            case TestStatus.Failed: return Color.red;
            default: return Color.white;
        }
    }

    private void ShowNoTestsMessage()
    {
        if (m_noTestsMessage != null)
        {
            m_noTestsMessage.style.display = DisplayStyle.Flex;
        }
        
        if (m_testsList != null)
        {
            m_testsList.Clear();
        }
    }

    private void HideNoTestsMessage()
    {
        if (m_noTestsMessage != null)
        {
            m_noTestsMessage.style.display = DisplayStyle.None;
        }
    }

    private void OnPrintButtonClicked()
    {
        Debug.Log("[PhlebotomyTerminalUIController] Print button clicked");
        OnPrintRequested?.Invoke();
    }

    public void SetMainContainerOutline(bool showOutline)
    {
        if (m_mainContainer == null)
        {
            Debug.LogWarning("[PhlebotomyTerminalUIController] Main container is null, cannot set outline");
            return;
        }

        if (showOutline)
        {
            m_mainContainer.AddToClassList("outline");
        }
        else
        {
            m_mainContainer.RemoveFromClassList("outline");
        }
    }
}