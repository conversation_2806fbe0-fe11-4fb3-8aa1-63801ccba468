// using UnityEngine;
// using System.Collections.Generic;
// using PurrNet;
// using LabTestSystem;

// /// <summary>
// /// Client tarafında test işlemlerini yöneten manager
// /// Server'a RPC istekleri gönderir ve gelen bildirimleri işler
// /// </summary>
// public class ClientTestManager : NetworkBehaviour
// {
//     private static ClientTestManager _instance;
//     public static ClientTestManager Instance
//     {
//         get
//         {
//             if (_instance == null)
//             {
//                 _instance = FindObjectOfType<ClientTestManager>();
//                 if (_instance == null)
//                 {
//                     GameObject go = new GameObject("ClientTestManager");
//                     _instance = go.AddComponent<ClientTestManager>();
//                 }
//             }
//             return _instance;
//         }
//     }

//     [Header("Current Session")]
//     [SerializeField] private string currentPatientId;
//     [SerializeField] private List<TestInstanceData> cachedPatientTests = new List<TestInstanceData>();
    
//     [Header("Network")]
//     [SerializeField] private NetworkManager networkManager;
//     private PlayerID localPlayerId;
    
//     // Client Events
//     public static event System.Action<TestInstanceData> OnTestCreatedLocal;
//     public static event System.Action<TestInstanceData> OnTestUpdatedLocal;
//     public static event System.Action<int> OnTestDeletedLocal;
//     public static event System.Action<TestInstanceData[]> OnPatientTestsReceived;
    
//     private void Awake()
//     {
//         if (_instance != null && _instance != this)
//         {
//             Destroy(gameObject);
//             return;
//         }
//         _instance = this;
//     }

//     private void Start()
//     {
//         // Network manager'ı bul
//         if (networkManager == null)
//             networkManager = FindObjectOfType<NetworkManager>();
            
//         // Database event'lerine abone ol
//         LabTestDatabase.OnClientTestCreated += HandleTestCreated;
//         LabTestDatabase.OnClientTestUpdated += HandleTestUpdated;
//         LabTestDatabase.OnClientTestDeleted += HandleTestDeleted;
        
//         // Local player ID'sini al
//         if (networkManager != null && networkManager.localPlayer != null)
//         {
//             localPlayerId = networkManager.localPlayer;
//         }
//     }

//     private void OnDestroy()
//     {
//         // Event aboneliklerini kaldır
//         LabTestDatabase.OnClientTestCreated -= HandleTestCreated;
//         LabTestDatabase.OnClientTestUpdated -= HandleTestUpdated;
//         LabTestDatabase.OnClientTestDeleted -= HandleTestDeleted;
//     }

//     #region Test Creation Requests

//     /// <summary>
//     /// Server'dan yeni test oluşturulmasını iste
//     /// </summary>
//     public void CreateTest(string patientId, string testDefinitionId)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log($"[CLIENT] Requesting test creation: {testDefinitionId} for patient {patientId}");
//         LabTestDatabase.Instance.RequestCreateTest(patientId, testDefinitionId, localPlayerId);
//     }

//     /// <summary>
//     /// Birden fazla test oluştur
//     /// </summary>
//     public void CreateMultipleTests(string patientId, List<string> testDefinitionIds)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log($"[CLIENT] Requesting {testDefinitionIds.Count} tests for patient {patientId}");
//         LabTestDatabase.Instance.RequestCreateMultipleTests(patientId, testDefinitionIds.ToArray(), localPlayerId);
//     }

//     #endregion

//     #region Test Update Requests

//     /// <summary>
//     /// Test sonucunu güncelle
//     /// </summary>
//     public void UpdateTestResult(int testInstanceId, string parameterName, string value)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log($"[CLIENT] Requesting test update: {testInstanceId} - {parameterName} = {value}");
//         LabTestDatabase.Instance.RequestUpdateTestResult(testInstanceId, parameterName, value, localPlayerId);
//     }

//     /// <summary>
//     /// Birden fazla test sonucunu güncelle
//     /// </summary>
//     public void UpdateMultipleTestResults(int testInstanceId, Dictionary<string, string> results)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         string[] paramNames = new string[results.Count];
//         string[] values = new string[results.Count];
        
//         int index = 0;
//         foreach (var kvp in results)
//         {
//             paramNames[index] = kvp.Key;
//             values[index] = kvp.Value;
//             index++;
//         }
        
//         Debug.Log($"[CLIENT] Requesting multiple parameter updates for test {testInstanceId}");
//         LabTestDatabase.Instance.RequestUpdateMultipleResults(testInstanceId, paramNames, values, localPlayerId);
//     }

//     /// <summary>
//     /// Testi tamamla
//     /// </summary>
//     public void CompleteTest(int testInstanceId)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log($"[CLIENT] Requesting test completion: {testInstanceId}");
//         LabTestDatabase.Instance.RequestCompleteTest(testInstanceId, localPlayerId);
//     }

//     #endregion

//     #region Test Query Requests

//     /// <summary>
//     /// Hastanın testlerini sorgula
//     /// </summary>
//     public void GetPatientTests(string patientId)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }

//         currentPatientId = patientId;
//         Debug.Log($"[CLIENT] GetPatientTests called for patient: {patientId}");
//         Debug.Log($"[CLIENT] LocalPlayerId: {localPlayerId}");
//         LabTestDatabase.Instance.RequestPatientTests(patientId, localPlayerId);
//         Debug.Log($"[CLIENT] RequestPatientTests sent to server");
//     }

//     /// <summary>
//     /// Belirli bir testi sorgula
//     /// </summary>
//     public void GetTestById(int testInstanceId)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log($"[CLIENT] Requesting test data: {testInstanceId}");
//         LabTestDatabase.Instance.RequestTestById(testInstanceId, localPlayerId);
//     }

//     /// <summary>
//     /// Test istatistiklerini sorgula
//     /// </summary>
//     public void GetTestStatistics()
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log("[CLIENT] Requesting test statistics");
//         LabTestDatabase.Instance.RequestTestStatistics(localPlayerId);
//     }

//     #endregion

//     #region Test Deletion Requests

//     /// <summary>
//     /// Test sil
//     /// </summary>
//     public void DeleteTest(int testInstanceId)
//     {
//         if (LabTestDatabase.Instance == null)
//         {
//             Debug.LogError("[CLIENT] LabTestDatabase not found!");
//             return;
//         }
        
//         Debug.Log($"[CLIENT] Requesting test deletion: {testInstanceId}");
//         LabTestDatabase.Instance.RequestDeleteTest(testInstanceId, localPlayerId);
//     }

//     #endregion

//     #region Event Handlers

//     private void HandleTestCreated(TestInstanceData testData)
//     {
//         Debug.Log($"[CLIENT] Test created: {testData.instanceTestId}");
        
//         // Eğer bu hastanın testi ise cache'e ekle
//         if (testData.patientId == currentPatientId)
//         {
//             cachedPatientTests.Add(testData);
//         }
        
//         OnTestCreatedLocal?.Invoke(testData);
//     }

//     private void HandleTestUpdated(TestInstanceData testData)
//     {
//         Debug.Log($"[CLIENT] Test updated: {testData.instanceTestId}");
        
//         // Cache'deki testi güncelle
//         for (int i = 0; i < cachedPatientTests.Count; i++)
//         {
//             if (cachedPatientTests[i].instanceTestId == testData.instanceTestId)
//             {
//                 cachedPatientTests[i] = testData;
//                 break;
//             }
//         }
        
//         OnTestUpdatedLocal?.Invoke(testData);
//     }

//     private void HandleTestDeleted(int testInstanceId)
//     {
//         Debug.Log($"[CLIENT] Test deleted: {testInstanceId}");
        
//         // Cache'den kaldır
//         cachedPatientTests.RemoveAll(t => t.instanceTestId == testInstanceId);
        
//         OnTestDeletedLocal?.Invoke(testInstanceId);
//     }

//     // Yeni method - Patient tests geldiğinde
//     public static void HandlePatientTestsReceived(TestInstanceData[] tests)
//     {
//         Debug.Log($"[CLIENT] HandlePatientTestsReceived called with {tests?.Length ?? 0} tests");

//         if (tests == null)
//         {
//             Debug.LogError("[CLIENT] HandlePatientTestsReceived: tests array is null!");
//             return;
//         }

//         // Cache'i güncelle
//         if (Instance != null)
//         {
//             Instance.cachedPatientTests.Clear();
//             Instance.cachedPatientTests.AddRange(tests);
//             Debug.Log($"[CLIENT] Updated cache with {tests.Length} tests");
//         }
//         else
//         {
//             Debug.LogError("[CLIENT] HandlePatientTestsReceived: Instance is null!");
//         }

//         // Event'i tetikle
//         Debug.Log($"[CLIENT] About to invoke OnPatientTestsReceived event");
//         OnPatientTestsReceived?.Invoke(tests);
//         Debug.Log($"[CLIENT] OnPatientTestsReceived event invoked");
//     }

//     #endregion

//     #region Helper Methods

//     /// <summary>
//     /// Cache'deki test verisini getir
//     /// </summary>
//     public TestInstanceData? GetCachedTest(int testInstanceId)
//     {
//         foreach (var test in cachedPatientTests)
//         {
//             if (test.instanceTestId == testInstanceId)
//                 return test;
//         }
//         return null;
//     }

//     /// <summary>
//     /// Cache'deki hasta testlerini getir
//     /// </summary>
//     public List<TestInstanceData> GetCachedPatientTests()
//     {
//         return new List<TestInstanceData>(cachedPatientTests);
//     }

//     /// <summary>
//     /// Cache'i temizle
//     /// </summary>
//     public void ClearCache()
//     {
//         cachedPatientTests.Clear();
//         currentPatientId = string.Empty;
//     }

//     /// <summary>
//     /// Test durumuna göre cache'deki testleri filtrele
//     /// </summary>
//     public List<TestInstanceData> GetCachedTestsByStatus(TestStatus status)
//     {
//         return cachedPatientTests.FindAll(t => t.status == status);
//     }

//     #endregion
// }