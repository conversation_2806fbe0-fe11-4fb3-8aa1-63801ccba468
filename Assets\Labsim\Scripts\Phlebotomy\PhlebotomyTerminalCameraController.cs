using UnityEngine;
using PurrNet;
using Unity.Cinemachine;
using UnityEngine.InputSystem;
using Labsim;

namespace Labsim
{
    public class PhlebotomyTerminalCameraController : NetworkBehaviour , ITerminalComponent
    {
        [Header("Properties")]
        [SerializeField] private int activeCameraPriority = 15;

        [SerializeField] private bool cameraControl = true;

        [Header("Phlebotomy Specific Cameras")]
        [SerializeField] private CinemachineCamera mainTerminalCamera;
        [SerializeField] private CinemachineCamera terminalScreenCamera;
        [SerializeField] private CinemachineCamera terminalMatchCamera;
        [SerializeField] private CinemachineCamera currentCinemachineCamera;
        
        [Header("Camera Settings")]
        [SerializeField] private float lookSensitivity = 2f;
        [SerializeField] private float maxLookAngle = 80f;

      

        private float verticalRotation = 0f;
        private float horizontalRotation = 0f;


        //Dependencies

        public PlayerInputHandler playerInputHandler;

        public ITerminalServiceProvider ServiceProvider { get; set; }

        protected override void OnSpawned()
        {
            base.OnSpawned();
            ServiceProvider.GetService<PhlebotomyTerminalInteractable>().OnTerminalEntered += HandleTerminalEntered;
            ServiceProvider.GetService<PhlebotomyTerminalInteractable>().OnTerminalExited += HandleTerminalExited;
        }

        protected override void OnDespawned()
        {
            ServiceProvider.GetService<PhlebotomyTerminalInteractable>().OnTerminalEntered -= HandleTerminalEntered;
            ServiceProvider.GetService<PhlebotomyTerminalInteractable>().OnTerminalExited -= HandleTerminalExited;
            base.OnDespawned();
        }

        //Listening events
        private void InitializeOwnerEvents()
        {
            ServiceProvider.GetService<PhlebotomyTerminal>().OnPatientScanned += HandlePatientScanned;
            ServiceProvider.GetService<PhlebotomyTerminalUIController>().OnPrintRequested += HandlePrintRequested;
            ServiceProvider.GetService<PhlebotomyScreenInteractable>().PhlebotomyScreenInteracted += HandleScreenInteracted;
        }

        private void UnInitializeOwnerEvents()
        {
            
            ServiceProvider.GetService<PhlebotomyTerminal>().OnPatientScanned -= HandlePatientScanned;
            ServiceProvider.GetService<PhlebotomyTerminalUIController>().OnPrintRequested -= HandlePrintRequested;
            ServiceProvider.GetService<PhlebotomyScreenInteractable>().PhlebotomyScreenInteracted -= HandleScreenInteracted;
        }

        //Listening events functions
        private void HandlePrintRequested()
        {
            currentCinemachineCamera = terminalMatchCamera;
            currentCinemachineCamera.Priority = activeCameraPriority;
            mainTerminalCamera.gameObject.SetActive(false);
            terminalScreenCamera.gameObject.SetActive(false);
            terminalMatchCamera.gameObject.SetActive(true);
            cameraControl = true;
            ServiceProvider.GetService<PlayerController>().PlayerCrosshair(true);
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        private void HandleTerminalEntered(IInteractor interactor)
        {
            playerInputHandler = ServiceProvider.GetService<PlayerInputHandler>();
            mainTerminalCamera.gameObject.SetActive(true);
            currentCinemachineCamera = mainTerminalCamera;
            currentCinemachineCamera.Priority = activeCameraPriority;
            verticalRotation = 6.4f;
            horizontalRotation = -85.9f;
            currentCinemachineCamera.transform.localRotation = Quaternion.Euler(6.4f, -85.9f, 0f);
            InitializeOwnerEvents();
        }

        private void HandleTerminalExited(IInteractor interactor)
        {
            mainTerminalCamera.gameObject.SetActive(false);
            terminalScreenCamera.gameObject.SetActive(false);
            terminalMatchCamera.gameObject.SetActive(false);
            currentCinemachineCamera = null;
            mainTerminalCamera.Priority = 0;
            terminalScreenCamera.Priority = 0;
            terminalMatchCamera.Priority = 0;
            cameraControl = true;
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            UnInitializeOwnerEvents();
        }

        private void HandleScreenInteracted()
        {
            currentCinemachineCamera = terminalScreenCamera;
            currentCinemachineCamera.Priority = activeCameraPriority;
            mainTerminalCamera.gameObject.SetActive(false);
            terminalScreenCamera.gameObject.SetActive(true);
            cameraControl = false;
            ServiceProvider.GetService<PlayerController>().PlayerCrosshair(false);
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }

        
        private void HandlePatientScanned(int patientId)
        {

        }

        private void Update()
        {
            if (!isOwner) return;
            if (cameraControl && currentCinemachineCamera != null && currentCinemachineCamera.Priority == activeCameraPriority)
            {
                HandleCameraRotation();
            }
        }


        private void HandleCameraRotation()
        {

            if (playerInputHandler == null || playerInputHandler.playerInput == null) return;
            Vector2 lookInput = playerInputHandler.playerInput.Terminal.TerminalLook.ReadValue<Vector2>();
            float mouseX = lookInput.x * lookSensitivity;
            float mouseY = lookInput.y * lookSensitivity;

            verticalRotation += mouseY;
            verticalRotation = Mathf.Clamp(verticalRotation, -maxLookAngle, maxLookAngle);
            
            horizontalRotation += mouseX;
            
            currentCinemachineCamera.transform.localRotation = Quaternion.Euler(verticalRotation, horizontalRotation, 0f);
        }


    }
}