using System;
using UnityEngine;
using PurrNet;
using System.Collections.Generic;
using System.Linq;
using Labsim;
using UnityEngine.Animations;

public class SocketManager : NetworkBehaviour
{
    [SerializeField] private List<Transform> socketTransforms = new List<Transform>();
    private Dictionary<Transform, GameObject> occupiedSockets = new Dictionary<Transform, GameObject>();
    private HashSet<Transform> lockedSockets = new HashSet<Transform>();
    
    public int AvailableSocketCount => socketTransforms.Count - occupiedSockets.Count;
    public int TotalSocketCount => socketTransforms.Count;
    public bool HasAvailableSocket => AvailableSocketCount > 0;
    
    public bool IsSocketAvailable(Transform socketTransform)
    {
        return socketTransforms.Contains(socketTransform) && !occupiedSockets.ContainsKey(socketTransform) && !lockedSockets.Contains(socketTransform);
    }
    
    public Transform GetFirstAvailableSocket()
    {
        return socketTransforms.FirstOrDefault(socket => !occupiedSockets.ContainsKey(socket) && !lockedSockets.Contains(socket));
    }
    
    public List<Transform> GetAllAvailableSockets()
    {
        return socketTransforms.Where(socket => !occupiedSockets.ContainsKey(socket) && !lockedSockets.Contains(socket)).ToList();
    }
    
    public bool ReserveSocket(Transform socketTransform)
    {
        if (socketTransform == null || !IsSocketAvailable(socketTransform))
        {
            return false;
        }
        
        lockedSockets.Add(socketTransform);
        return true;
    }
    
    public void ReleaseSocketReservation(Transform socketTransform)
    {
        if (socketTransform != null)
        {
            lockedSockets.Remove(socketTransform);
        }
    }
    
    public Transform ReserveFirstAvailableSocket()
    {
        Transform socket = GetFirstAvailableSocket();
        if (socket != null && ReserveSocket(socket))
        {
            return socket;
        }
        return null;
    }
    
    public bool AttachToSocket(GameObject objectToAttach)
    {
        if (objectToAttach == null)
        {
            Debug.LogWarning("SocketManager: Cannot attach null object");
            return false;
        }
        
        //NetworkObject networkObject = objectToAttach.GetComponent<NetworkObject>();
        // if (networkObject == null)
        // {
        //     Debug.LogWarning($"SocketManager: Object {objectToAttach.name} does not have NetworkObject component");
        //     return false;
        // }
        
        ParentConstraint parentConstraint = objectToAttach.GetComponent<ParentConstraint>();
        if (parentConstraint == null)
        {
            Debug.LogWarning($"SocketManager: Object {objectToAttach.name} does not have ParentConstraint component");
            return false;
        }
        
        Transform availableSocket = GetFirstAvailableSocket();
        if (availableSocket == null)
        {
            Debug.LogWarning("SocketManager: No available sockets");
            return false;
        }
        
        
        // if (!networkObject.TrySetParent(this.NetworkObject))
        // {
        //     Debug.LogWarning($"SocketManager: Failed to set parent for NetworkObject {objectToAttach.name}");
        //     Debug.LogWarning($"SocketManager: NetworkObject spawn status - Target: {networkObject.IsSpawned}, Manager: {this.NetworkObject.IsSpawned}");
        //     return false;
        // }
        
        ConstraintSource source = new ConstraintSource
        {
            sourceTransform = availableSocket,
            weight = 1.0f
        };
        
        parentConstraint.AddSource(source);
        parentConstraint.constraintActive = true;
        
        occupiedSockets[availableSocket] = objectToAttach;
        lockedSockets.Remove(availableSocket);
        
        Debug.Log($"SocketManager: Successfully attached {objectToAttach.name} to socket {availableSocket.name}");
        return true;
    }
    
    public bool AttachToSpecificSocket(GameObject objectToAttach, Transform targetSocket)
    {
        if (objectToAttach == null)
        {
            Debug.LogWarning("SocketManager: Cannot attach null object");
            return false;
        }
        
        if (targetSocket == null || !socketTransforms.Contains(targetSocket))
        {
            Debug.LogWarning("SocketManager: Invalid target socket");
            return false;
        }
        
        if (occupiedSockets.ContainsKey(targetSocket))
        {
            Debug.LogWarning($"SocketManager: Socket {targetSocket.name} is already occupied");
            return false;
        }
        
        // NetworkObject networkObject = objectToAttach.GetComponent<NetworkObject>();
        // if (networkObject == null)
        // {
        //     Debug.LogWarning($"SocketManager: Object {objectToAttach.name} does not have NetworkObject component");
        //     return false;
        // }
        
        ParentConstraint parentConstraint = objectToAttach.GetComponent<ParentConstraint>();
        if (parentConstraint == null)
        {
            Debug.LogWarning($"SocketManager: Object {objectToAttach.name} does not have ParentConstraint component");
            return false;
        }
        
        // if (!networkObject.TrySetParent(this.NetworkObject))
        // {
        //     Debug.LogWarning($"SocketManager: Failed to set parent for NetworkObject {objectToAttach.name}");
        //     return false;
        // }
        
        ConstraintSource source = new ConstraintSource
        {
            sourceTransform = targetSocket,
            weight = 1.0f
        };
        
        parentConstraint.AddSource(source);
        parentConstraint.constraintActive = true;
        
        occupiedSockets[targetSocket] = objectToAttach;
        lockedSockets.Remove(targetSocket);
        
        Debug.Log($"SocketManager: Successfully attached {objectToAttach.name} to specific socket {targetSocket.name}");
        return true;
    }
    
    public bool DetachFromSocket(GameObject objectToDetach)
    {
        if (objectToDetach == null)
        {
            Debug.LogWarning("SocketManager: Cannot detach null object");
            return false;
        }
        
        Transform socketToFree = occupiedSockets.FirstOrDefault(pair => pair.Value == objectToDetach).Key;
        if (socketToFree == null)
        {
            Debug.LogWarning($"SocketManager: Object {objectToDetach.name} is not attached to any socket");
            return false;
        }
        
        ParentConstraint parentConstraint = objectToDetach.GetComponent<ParentConstraint>();
        if (parentConstraint != null)
        {
            for (int i = parentConstraint.sourceCount - 1; i >= 0; i--)
            {
                ConstraintSource source = parentConstraint.GetSource(i);
                if (source.sourceTransform == socketToFree)
                {
                    parentConstraint.RemoveSource(i);
                    break;
                }
            }
            
            if (parentConstraint.sourceCount == 0)
            {
                parentConstraint.constraintActive = false;
            }
        }
        
        occupiedSockets.Remove(socketToFree);
        
        Debug.Log($"SocketManager: Successfully detached {objectToDetach.name} from socket {socketToFree.name}");
        return true;
    }
    
    public GameObject GetAttachedObject(Transform socketTransform)
    {
        return occupiedSockets.TryGetValue(socketTransform, out GameObject attachedObject) ? attachedObject : null;
    }
    
    public Transform GetSocketForObject(GameObject attachedObject)
    {
        return occupiedSockets.FirstOrDefault(pair => pair.Value == attachedObject).Key;
    }
}