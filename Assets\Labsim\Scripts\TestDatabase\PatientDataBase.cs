using System;
using System.Collections.Generic;
using PurrNet;
using UnityEngine;
using System.Threading;

public class PatientDataBase : NetworkBehaviour
{

    // Dictionary to store all patient data with patientId as key
    private Dictionary<int, PatientData> patientDatabase = new Dictionary<int, PatientData>();
    
    private static int nextPatientId = 2000;

    // Create a new patient and add it to the database
    public PatientData CreatePatient(string patientName, int age, Gender gender)
    {
        int patientId = Interlocked.Increment(ref nextPatientId);
        PatientData newPatient = new PatientData(patientId, patientName, age, gender);

        lock (patientDatabase)
        {
            if (patientDatabase.ContainsKey(patientId))
            {
                throw new InvalidOperationException($"Duplicate ID generated: {patientId}. This should not happen with Interlocked.Increment.");
            }
            patientDatabase[patientId] = newPatient;
        }
        
        return newPatient;
    }

    // Get a patient by ID
    public PatientData GetPatient(int patientId)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            return patient;
        }
        return null;
    }

    // Update patient name
    public bool UpdatePatientName(int patientId, string newName)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            patient.patientName = newName;
            return true;
        }
        return false;
    }

    // Update patient date of birth
    public bool UpdatePatientAge(int patientId, int newAge)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            patient.age = newAge;
            return true;
        }
        return false;
    }

    // Update patient gender
    public bool UpdatePatientGender(int patientId, Gender newGender)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            patient.gender = newGender;
            return true;
        }
        return false;
    }

    // Assign a test to a patient
    public bool AssignTestToPatient(int patientId, int testId)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            if (!patient.assignedTests.Contains(testId))
            {
                patient.assignedTests.Add(testId);
                return true;
            }
        }
        return false;
    }

    // Remove a test from a patient
    public bool RemoveTestFromPatient(int patientId, int testId)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            return patient.assignedTests.Remove(testId);
        }
        return false;
    }

    // Get all tests assigned to a patient
    public List<int> GetPatientTests(int patientId)
    {
        if (patientDatabase.TryGetValue(patientId, out PatientData patient))
        {
            return new List<int>(patient.assignedTests);
        }
        return new List<int>();
    }

    // Delete a patient from the database
    public bool DeletePatient(int patientId)
    {
        return patientDatabase.Remove(patientId);
    }

    // Get all patients in the database
    public List<PatientData> GetAllPatients()
    {
        return new List<PatientData>(patientDatabase.Values);
    }

    // Search patients by name
    public List<PatientData> SearchPatientsByName(string nameQuery)
    {
        List<PatientData> results = new List<PatientData>();
        foreach (var patient in patientDatabase.Values)
        {
            if (patient.patientName.Contains(nameQuery, StringComparison.OrdinalIgnoreCase))
            {
                results.Add(patient);
            }
        }
        return results;
    }
}
